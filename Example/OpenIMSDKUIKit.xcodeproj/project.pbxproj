// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0E22FE6F2D641ED200440976 /* IntentFeedViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E22FE6D2D641ED200440976 /* IntentFeedViewController.swift */; };
		0E22FE702D641ED200440976 /* FollowService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E22FE692D641ED200440976 /* FollowService.swift */; };
		0E22FE722D641ED200440976 /* IntentFeedModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E22FE672D641ED200440976 /* IntentFeedModel.swift */; };
		0E22FE732D641ED200440976 /* FollowModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E22FE662D641ED200440976 /* FollowModel.swift */; };
		0E2458592D9D8A25008C748A /* FeedDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E2458582D9D8A25008C748A /* FeedDetailViewController.swift */; };
		0E24585B2D9E2192008C748A /* IntentFeedCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E24585A2D9E2192008C748A /* IntentFeedCell.swift */; };
		0E4234102DD1942E0033D520 /* LocalizationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E42340E2DD1942E0033D520 /* LocalizationManager.swift */; };
		0E4234142DD32A100033D520 /* EmailLoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E4234122DD32A0F0033D520 /* EmailLoginViewController.swift */; };
		0E4234152DD32A100033D520 /* EmailRegisterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E4234132DD32A0F0033D520 /* EmailRegisterViewController.swift */; };
		0E4234162DD32A100033D520 /* EmailForgotPasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E4234112DD32A0F0033D520 /* EmailForgotPasswordViewController.swift */; };
		0E7068CE2E39B90B0098CBEB /* ProgressHUD+Custom.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E7068CC2E39B90B0098CBEB /* ProgressHUD+Custom.swift */; };
		0E8216A12D194CCD003725CB /* PublishIntentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E8216A02D194CCD003725CB /* PublishIntentViewController.swift */; };
		0EA4F2C92DE057F7000F4B33 /* PhoneRegisterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0EA4F2C82DE057F7000F4B33 /* PhoneRegisterViewController.swift */; };
		0EB4A8332CC0A84300B7C494 /* CameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0EB4A8322CC0A84300B7C494 /* CameraViewController.swift */; };
		0EB679132DCC4D56005BEF43 /* LanguageSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0EB679122DCC4D56005BEF43 /* LanguageSettingViewController.swift */; };
		170703BC2B440A8800DDD2FC /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 170703BB2B440A8800DDD2FC /* libresolv.tbd */; };
		170A2B3928F29C5000CA68EA /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 170A2B3828F29C5000CA68EA /* NotificationService.swift */; };
		170A2B3D28F29C5000CA68EA /* NotificationService.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 170A2B3628F29C5000CA68EA /* NotificationService.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		1780764A29260BF50024399B /* InputPasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1780764929260BF50024399B /* InputPasswordViewController.swift */; };
		1790EE6C2921EAFC00C4024A /* BlockedListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE5B2921EAFC00C4024A /* BlockedListViewController.swift */; };
		1790EE6D2921EAFC00C4024A /* SettingTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE5C2921EAFC00C4024A /* SettingTableViewController.swift */; };
		1790EE6E2921EAFC00C4024A /* CheckBoxTextTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE5D2921EAFC00C4024A /* CheckBoxTextTableViewCell.swift */; };
		1790EE6F2921EAFC00C4024A /* SettingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE5E2921EAFC00C4024A /* SettingViewModel.swift */; };
		1790EE712921EAFC00C4024A /* MineOptionTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE602921EAFC00C4024A /* MineOptionTableViewCell.swift */; };
		1790EE722921EAFC00C4024A /* MineViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE612921EAFC00C4024A /* MineViewModel.swift */; };
		1790EE742921EAFC00C4024A /* MineViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE632921EAFC00C4024A /* MineViewController.swift */; };
		1790EE762921EAFC00C4024A /* ConfigViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE662921EAFC00C4024A /* ConfigViewController.swift */; };
		1790EE792921EAFC00C4024A /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE692921EAFC00C4024A /* LoginViewController.swift */; };
		1790EE7A2921EAFC00C4024A /* MainTabViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE6A2921EAFC00C4024A /* MainTabViewController.swift */; };
		1790EE7F2922220D00C4024A /* ProfileTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE7E2922220D00C4024A /* ProfileTableViewController.swift */; };
		1790EE812922294600C4024A /* AccountViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1790EE802922294600C4024A /* AccountViewModel.swift */; };
		17BBFAC02925C6330025B686 /* InputAccountViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17BBFABF2925C6330025B686 /* InputAccountViewController.swift */; };
		17BBFAC22925CDBB0025B686 /* InputCodeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17BBFAC12925CDBB0025B686 /* InputCodeViewController.swift */; };
		17BBFAC42925D4C10025B686 /* CountDownTimerButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17BBFAC32925D4C10025B686 /* CountDownTimerButton.swift */; };
		17C370A22A4DA5EF00CCB044 /* String+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17C370A12A4DA5EF00CCB044 /* String+Extension.swift */; };
		17C4BBAA28CA1220003782EF /* Array+deduplicate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17C4BBA928CA1220003782EF /* Array+deduplicate.swift */; };
		607FACD61AFB9204008FA782 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 607FACD51AFB9204008FA782 /* AppDelegate.swift */; };
		607FACDB1AFB9204008FA782 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 607FACD91AFB9204008FA782 /* Main.storyboard */; };
		607FACDD1AFB9204008FA782 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 607FACDC1AFB9204008FA782 /* Images.xcassets */; };
		607FACE01AFB9204008FA782 /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 607FACDE1AFB9204008FA782 /* LaunchScreen.xib */; };
		83FD2AE3451D431FC78FBF4B /* Pods_OpenIMSDKUIKit_Example.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C0B2D5648A34E5BE2A5E92FE /* Pods_OpenIMSDKUIKit_Example.framework */; };
		DC0B7DF2282DFA09003896C1 /* UnderlineButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = DC0B7DF1282DFA09003896C1 /* UnderlineButton.swift */; };
		DC0B7DF4282DFA23003896C1 /* UnderlineTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = DC0B7DF3282DFA23003896C1 /* UnderlineTextField.swift */; };
		DC0B7DF6282DFA85003896C1 /* DemoUI.swift in Sources */ = {isa = PBXBuildFile; fileRef = DC0B7DF5282DFA85003896C1 /* DemoUI.swift */; };
		DCE04171283E5BD30095874E /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = DCE04173283E5BD30095874E /* Localizable.strings */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		170A2B3B28F29C5000CA68EA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 607FACC81AFB9204008FA782 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 170A2B3528F29C5000CA68EA;
			remoteInfo = NotificationService;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		170A2B3E28F29C5000CA68EA /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				170A2B3D28F29C5000CA68EA /* NotificationService.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0E22FE662D641ED200440976 /* FollowModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowModel.swift; sourceTree = "<group>"; };
		0E22FE672D641ED200440976 /* IntentFeedModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntentFeedModel.swift; sourceTree = "<group>"; };
		0E22FE692D641ED200440976 /* FollowService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowService.swift; sourceTree = "<group>"; };
		0E22FE6D2D641ED200440976 /* IntentFeedViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntentFeedViewController.swift; sourceTree = "<group>"; };
		0E2458582D9D8A25008C748A /* FeedDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedDetailViewController.swift; sourceTree = "<group>"; };
		0E24585A2D9E2192008C748A /* IntentFeedCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntentFeedCell.swift; sourceTree = "<group>"; };
		0E42340E2DD1942E0033D520 /* LocalizationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocalizationManager.swift; sourceTree = "<group>"; };
		0E4234112DD32A0F0033D520 /* EmailForgotPasswordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailForgotPasswordViewController.swift; sourceTree = "<group>"; };
		0E4234122DD32A0F0033D520 /* EmailLoginViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailLoginViewController.swift; sourceTree = "<group>"; };
		0E4234132DD32A0F0033D520 /* EmailRegisterViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailRegisterViewController.swift; sourceTree = "<group>"; };
		0E7068CC2E39B90B0098CBEB /* ProgressHUD+Custom.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ProgressHUD+Custom.swift"; sourceTree = "<group>"; };
		0E8216A02D194CCD003725CB /* PublishIntentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PublishIntentViewController.swift; sourceTree = "<group>"; };
		0EA4F2C82DE057F7000F4B33 /* PhoneRegisterViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhoneRegisterViewController.swift; sourceTree = "<group>"; };
		0EB4A8322CC0A84300B7C494 /* CameraViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = CameraViewController.swift; path = OpenIMSDKUIKit/Pages/CameraViewController.swift; sourceTree = "<group>"; };
		0EB679122DCC4D56005BEF43 /* LanguageSettingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageSettingViewController.swift; sourceTree = "<group>"; };
		170703BB2B440A8800DDD2FC /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		170A2B3628F29C5000CA68EA /* NotificationService.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NotificationService.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		170A2B3828F29C5000CA68EA /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		170A2B3A28F29C5000CA68EA /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		170A2B4228F29E8D00CA68EA /* NotificationService.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = NotificationService.entitlements; sourceTree = "<group>"; };
		1780764929260BF50024399B /* InputPasswordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputPasswordViewController.swift; sourceTree = "<group>"; };
		1790EE5B2921EAFC00C4024A /* BlockedListViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BlockedListViewController.swift; sourceTree = "<group>"; };
		1790EE5C2921EAFC00C4024A /* SettingTableViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingTableViewController.swift; sourceTree = "<group>"; };
		1790EE5D2921EAFC00C4024A /* CheckBoxTextTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CheckBoxTextTableViewCell.swift; sourceTree = "<group>"; };
		1790EE5E2921EAFC00C4024A /* SettingViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingViewModel.swift; sourceTree = "<group>"; };
		1790EE602921EAFC00C4024A /* MineOptionTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MineOptionTableViewCell.swift; sourceTree = "<group>"; };
		1790EE612921EAFC00C4024A /* MineViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MineViewModel.swift; sourceTree = "<group>"; };
		1790EE632921EAFC00C4024A /* MineViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MineViewController.swift; sourceTree = "<group>"; };
		1790EE662921EAFC00C4024A /* ConfigViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ConfigViewController.swift; sourceTree = "<group>"; };
		1790EE692921EAFC00C4024A /* LoginViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoginViewController.swift; sourceTree = "<group>"; };
		1790EE6A2921EAFC00C4024A /* MainTabViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainTabViewController.swift; sourceTree = "<group>"; };
		1790EE7E2922220D00C4024A /* ProfileTableViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = ProfileTableViewController.swift; path = OpenIMSDKUIKit/Pages/Mine/ProfileTableViewController.swift; sourceTree = SOURCE_ROOT; };
		1790EE802922294600C4024A /* AccountViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AccountViewModel.swift; sourceTree = "<group>"; };
		17B485F529C082CA002571B8 /* ReplayKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ReplayKit.framework; path = System/Library/Frameworks/ReplayKit.framework; sourceTree = SDKROOT; };
		17BBFABF2925C6330025B686 /* InputAccountViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputAccountViewController.swift; sourceTree = "<group>"; };
		17BBFAC12925CDBB0025B686 /* InputCodeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputCodeViewController.swift; sourceTree = "<group>"; };
		17BBFAC32925D4C10025B686 /* CountDownTimerButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CountDownTimerButton.swift; sourceTree = "<group>"; };
		17C370A12A4DA5EF00CCB044 /* String+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "String+Extension.swift"; sourceTree = "<group>"; };
		17C4BBA928CA1220003782EF /* Array+deduplicate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Array+deduplicate.swift"; sourceTree = "<group>"; };
		607FACD01AFB9204008FA782 /* Chill.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Chill.app; sourceTree = BUILT_PRODUCTS_DIR; };
		607FACD41AFB9204008FA782 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		607FACD51AFB9204008FA782 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		607FACDA1AFB9204008FA782 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		607FACDC1AFB9204008FA782 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		607FACDF1AFB9204008FA782 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		742F25766271FC0EB630862F /* Pods-OpenIMSDKUIKit_Example.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OpenIMSDKUIKit_Example.release.xcconfig"; path = "Target Support Files/Pods-OpenIMSDKUIKit_Example/Pods-OpenIMSDKUIKit_Example.release.xcconfig"; sourceTree = "<group>"; };
		BAF9BF6ADBCA82ACA78EB9E4 /* Pods-OpenIMSDKUIKit_Example.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OpenIMSDKUIKit_Example.debug.xcconfig"; path = "Target Support Files/Pods-OpenIMSDKUIKit_Example/Pods-OpenIMSDKUIKit_Example.debug.xcconfig"; sourceTree = "<group>"; };
		C0B2D5648A34E5BE2A5E92FE /* Pods_OpenIMSDKUIKit_Example.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_OpenIMSDKUIKit_Example.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DC05D4CF283E153400CC3974 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Main.strings"; sourceTree = "<group>"; };
		DC05D4D0283E156300CC3974 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Main.strings; sourceTree = "<group>"; };
		DC05D4D1283E156300CC3974 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		DC0B7DF1282DFA09003896C1 /* UnderlineButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UnderlineButton.swift; sourceTree = "<group>"; };
		DC0B7DF3282DFA23003896C1 /* UnderlineTextField.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnderlineTextField.swift; sourceTree = "<group>"; };
		DC0B7DF5282DFA85003896C1 /* DemoUI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DemoUI.swift; sourceTree = "<group>"; };
		DC1FAF0F27FF04D000B529B9 /* OpenIMSDKUIKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = OpenIMSDKUIKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DC73AEDB284CFDD200F98906 /* OpenIMSDKUIKit_Example-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "OpenIMSDKUIKit_Example-Bridging-Header.h"; sourceTree = "<group>"; };
		DCD9C9762849145C00487806 /* OpenIMSDKUIKit_Example.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = OpenIMSDKUIKit_Example.entitlements; sourceTree = "<group>"; };
		DCE04172283E5BD30095874E /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		DCE04174283E5BD70095874E /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		EED869B95C353B3B8D5FE5BE /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = net.daringfireball.markdown; name = README.md; path = ../README.md; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		170A2B3328F29C5000CA68EA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		607FACCD1AFB9204008FA782 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				170703BC2B440A8800DDD2FC /* libresolv.tbd in Frameworks */,
				83FD2AE3451D431FC78FBF4B /* Pods_OpenIMSDKUIKit_Example.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0E22FE682D641ED200440976 /* Models */ = {
			isa = PBXGroup;
			children = (
				0E22FE662D641ED200440976 /* FollowModel.swift */,
				0E22FE672D641ED200440976 /* IntentFeedModel.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		0E22FE6A2D641ED200440976 /* Services */ = {
			isa = PBXGroup;
			children = (
				0E22FE692D641ED200440976 /* FollowService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		0E22FE6C2D641ED200440976 /* Views */ = {
			isa = PBXGroup;
			children = (
				0E24585A2D9E2192008C748A /* IntentFeedCell.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		0E22FE6E2D641ED200440976 /* IntentFeed */ = {
			isa = PBXGroup;
			children = (
				0E2458582D9D8A25008C748A /* FeedDetailViewController.swift */,
				0E22FE682D641ED200440976 /* Models */,
				0E22FE6A2D641ED200440976 /* Services */,
				0E22FE6C2D641ED200440976 /* Views */,
				0E22FE6D2D641ED200440976 /* IntentFeedViewController.swift */,
			);
			path = IntentFeed;
			sourceTree = "<group>";
		};
		0E42340F2DD1942E0033D520 /* Localization */ = {
			isa = PBXGroup;
			children = (
				0E42340E2DD1942E0033D520 /* LocalizationManager.swift */,
			);
			path = Localization;
			sourceTree = "<group>";
		};
		170A2B3728F29C5000CA68EA /* NotificationService */ = {
			isa = PBXGroup;
			children = (
				170A2B4228F29E8D00CA68EA /* NotificationService.entitlements */,
				170A2B3828F29C5000CA68EA /* NotificationService.swift */,
				170A2B3A28F29C5000CA68EA /* Info.plist */,
			);
			path = NotificationService;
			sourceTree = "<group>";
		};
		1790EE592921EAFC00C4024A /* Mine */ = {
			isa = PBXGroup;
			children = (
				1790EE5A2921EAFC00C4024A /* Settings */,
				1790EE602921EAFC00C4024A /* MineOptionTableViewCell.swift */,
				1790EE612921EAFC00C4024A /* MineViewModel.swift */,
				1790EE632921EAFC00C4024A /* MineViewController.swift */,
				1790EE7E2922220D00C4024A /* ProfileTableViewController.swift */,
			);
			path = Mine;
			sourceTree = "<group>";
		};
		1790EE5A2921EAFC00C4024A /* Settings */ = {
			isa = PBXGroup;
			children = (
				0EB679122DCC4D56005BEF43 /* LanguageSettingViewController.swift */,
				1790EE5B2921EAFC00C4024A /* BlockedListViewController.swift */,
				1790EE5C2921EAFC00C4024A /* SettingTableViewController.swift */,
				1790EE5D2921EAFC00C4024A /* CheckBoxTextTableViewCell.swift */,
				1790EE5E2921EAFC00C4024A /* SettingViewModel.swift */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		1790EE642921EAFC00C4024A /* Account */ = {
			isa = PBXGroup;
			children = (
				0EA4F2C82DE057F7000F4B33 /* PhoneRegisterViewController.swift */,
				0E4234112DD32A0F0033D520 /* EmailForgotPasswordViewController.swift */,
				0E4234122DD32A0F0033D520 /* EmailLoginViewController.swift */,
				0E4234132DD32A0F0033D520 /* EmailRegisterViewController.swift */,
				1790EE802922294600C4024A /* AccountViewModel.swift */,
				1790EE662921EAFC00C4024A /* ConfigViewController.swift */,
				1790EE692921EAFC00C4024A /* LoginViewController.swift */,
				17BBFABF2925C6330025B686 /* InputAccountViewController.swift */,
				1780764929260BF50024399B /* InputPasswordViewController.swift */,
				17BBFAC12925CDBB0025B686 /* InputCodeViewController.swift */,
				17BBFAC32925D4C10025B686 /* CountDownTimerButton.swift */,
			);
			path = Account;
			sourceTree = "<group>";
		};
		607FACC71AFB9204008FA782 = {
			isa = PBXGroup;
			children = (
				0EB4A8322CC0A84300B7C494 /* CameraViewController.swift */,
				DCD9C9762849145C00487806 /* OpenIMSDKUIKit_Example.entitlements */,
				607FACF51AFB993E008FA782 /* Podspec Metadata */,
				170A2B3728F29C5000CA68EA /* NotificationService */,
				607FACD11AFB9204008FA782 /* Products */,
				CAC64E6711B5A6903B4FCC8D /* Pods */,
				641162BB15A7DD76EBF4C191 /* Frameworks */,
				607FACD21AFB9204008FA782 /* Example for OpenIMSDKUIKit */,
			);
			sourceTree = "<group>";
		};
		607FACD11AFB9204008FA782 /* Products */ = {
			isa = PBXGroup;
			children = (
				607FACD01AFB9204008FA782 /* Chill.app */,
				170A2B3628F29C5000CA68EA /* NotificationService.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		607FACD21AFB9204008FA782 /* Example for OpenIMSDKUIKit */ = {
			isa = PBXGroup;
			children = (
				0E42340F2DD1942E0033D520 /* Localization */,
				DC0B7DEF282DF9F4003896C1 /* Common */,
				DC1FAF0A27FE8E6B00B529B9 /* Utils */,
				************************ /* Pages */,
				607FACD51AFB9204008FA782 /* AppDelegate.swift */,
				607FACD91AFB9204008FA782 /* Main.storyboard */,
				607FACDC1AFB9204008FA782 /* Images.xcassets */,
				607FACDE1AFB9204008FA782 /* LaunchScreen.xib */,
				607FACD31AFB9204008FA782 /* Supporting Files */,
				DCE04173283E5BD30095874E /* Localizable.strings */,
				DC73AEDB284CFDD200F98906 /* OpenIMSDKUIKit_Example-Bridging-Header.h */,
			);
			name = "Example for OpenIMSDKUIKit";
			path = OpenIMSDKUIKit;
			sourceTree = "<group>";
		};
		607FACD31AFB9204008FA782 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				607FACD41AFB9204008FA782 /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		607FACF51AFB993E008FA782 /* Podspec Metadata */ = {
			isa = PBXGroup;
			children = (
				EED869B95C353B3B8D5FE5BE /* README.md */,
			);
			name = "Podspec Metadata";
			sourceTree = "<group>";
		};
		641162BB15A7DD76EBF4C191 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				170703BB2B440A8800DDD2FC /* libresolv.tbd */,
				DC1FAF0F27FF04D000B529B9 /* OpenIMSDKUIKit.framework */,
				17B485F529C082CA002571B8 /* ReplayKit.framework */,
				C0B2D5648A34E5BE2A5E92FE /* Pods_OpenIMSDKUIKit_Example.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		CAC64E6711B5A6903B4FCC8D /* Pods */ = {
			isa = PBXGroup;
			children = (
				BAF9BF6ADBCA82ACA78EB9E4 /* Pods-OpenIMSDKUIKit_Example.debug.xcconfig */,
				742F25766271FC0EB630862F /* Pods-OpenIMSDKUIKit_Example.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		DC0B7DEF282DF9F4003896C1 /* Common */ = {
			isa = PBXGroup;
			children = (
				0E7068CC2E39B90B0098CBEB /* ProgressHUD+Custom.swift */,
				DC0B7DF0282DF9FF003896C1 /* Views */,
				DC0B7DF5282DFA85003896C1 /* DemoUI.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		DC0B7DF0282DF9FF003896C1 /* Views */ = {
			isa = PBXGroup;
			children = (
				DC0B7DF1282DFA09003896C1 /* UnderlineButton.swift */,
				DC0B7DF3282DFA23003896C1 /* UnderlineTextField.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		DC1FAF0A27FE8E6B00B529B9 /* Utils */ = {
			isa = PBXGroup;
			children = (
				17C4BBA928CA1220003782EF /* Array+deduplicate.swift */,
				17C370A12A4DA5EF00CCB044 /* String+Extension.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		************************ /* Pages */ = {
			isa = PBXGroup;
			children = (
				0E22FE6E2D641ED200440976 /* IntentFeed */,
				0E8216A02D194CCD003725CB /* PublishIntentViewController.swift */,
				1790EE6A2921EAFC00C4024A /* MainTabViewController.swift */,
				1790EE642921EAFC00C4024A /* Account */,
				1790EE592921EAFC00C4024A /* Mine */,
			);
			path = Pages;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		170A2B3528F29C5000CA68EA /* NotificationService */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 170A2B4128F29C5000CA68EA /* Build configuration list for PBXNativeTarget "NotificationService" */;
			buildPhases = (
				170A2B3228F29C5000CA68EA /* Sources */,
				170A2B3328F29C5000CA68EA /* Frameworks */,
				170A2B3428F29C5000CA68EA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NotificationService;
			productName = NotificationService;
			productReference = 170A2B3628F29C5000CA68EA /* NotificationService.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		607FACCF1AFB9204008FA782 /* OpenIMSDKUIKit_Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 607FACEF1AFB9204008FA782 /* Build configuration list for PBXNativeTarget "OpenIMSDKUIKit_Example" */;
			buildPhases = (
				B0543E0AF23A91CC376A2734 /* [CP] Check Pods Manifest.lock */,
				DCBD4C0227F07EE7003AFE6A /* Run Lint */,
				607FACCC1AFB9204008FA782 /* Sources */,
				607FACCD1AFB9204008FA782 /* Frameworks */,
				607FACCE1AFB9204008FA782 /* Resources */,
				170A2B3E28F29C5000CA68EA /* Embed App Extensions */,
				91613A28B0F72D365B686F6D /* [CP] Embed Pods Frameworks */,
				9DEED09E9EDFC150DA989F99 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				170A2B3C28F29C5000CA68EA /* PBXTargetDependency */,
			);
			name = OpenIMSDKUIKit_Example;
			productName = OpenIMSDKUIKit;
			productReference = 607FACD01AFB9204008FA782 /* Chill.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		607FACC81AFB9204008FA782 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1410;
				LastUpgradeCheck = 0830;
				ORGANIZATIONNAME = rentsoft;
				TargetAttributes = {
					170A2B3528F29C5000CA68EA = {
						CreatedOnToolsVersion = 13.4;
						DevelopmentTeam = H73T6ANG2B;
						ProvisioningStyle = Automatic;
					};
					607FACCF1AFB9204008FA782 = {
						CreatedOnToolsVersion = 6.3.1;
						DevelopmentTeam = H73T6ANG2B;
						LastSwiftMigration = 1340;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 607FACCB1AFB9204008FA782 /* Build configuration list for PBXProject "OpenIMSDKUIKit" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				"zh-Hans",
				en,
			);
			mainGroup = 607FACC71AFB9204008FA782;
			productRefGroup = 607FACD11AFB9204008FA782 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				607FACCF1AFB9204008FA782 /* OpenIMSDKUIKit_Example */,
				170A2B3528F29C5000CA68EA /* NotificationService */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		170A2B3428F29C5000CA68EA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		607FACCE1AFB9204008FA782 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				607FACDB1AFB9204008FA782 /* Main.storyboard in Resources */,
				DCE04171283E5BD30095874E /* Localizable.strings in Resources */,
				607FACE01AFB9204008FA782 /* LaunchScreen.xib in Resources */,
				607FACDD1AFB9204008FA782 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		91613A28B0F72D365B686F6D /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OpenIMSDKUIKit_Example/Pods-OpenIMSDKUIKit_Example-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/Alamofire/Alamofire.framework",
				"${BUILT_PRODUCTS_DIR}/ChatLayout/ChatLayout.framework",
				"${BUILT_PRODUCTS_DIR}/DifferenceKit/DifferenceKit.framework",
				"${BUILT_PRODUCTS_DIR}/Differentiator/Differentiator.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManagerSwift/IQKeyboardManagerSwift.framework",
				"${BUILT_PRODUCTS_DIR}/InputBarAccessoryView/InputBarAccessoryView.framework",
				"${BUILT_PRODUCTS_DIR}/JXSegmentedView/JXSegmentedView.framework",
				"${BUILT_PRODUCTS_DIR}/Kingfisher/Kingfisher.framework",
				"${BUILT_PRODUCTS_DIR}/Lantern/Lantern.framework",
				"${BUILT_PRODUCTS_DIR}/LiveKitClient/LiveKitClient.framework",
				"${BUILT_PRODUCTS_DIR}/Localize-Swift/Localize_Swift.framework",
				"${BUILT_PRODUCTS_DIR}/Logging/Logging.framework",
				"${BUILT_PRODUCTS_DIR}/MJExtension/MJExtension.framework",
				"${BUILT_PRODUCTS_DIR}/MJRefresh/MJRefresh.framework",
				"${BUILT_PRODUCTS_DIR}/MMBAlertsPickers/MMBAlertsPickers.framework",
				"${BUILT_PRODUCTS_DIR}/ProgressHUD/ProgressHUD.framework",
				"${BUILT_PRODUCTS_DIR}/RxCocoa/RxCocoa.framework",
				"${BUILT_PRODUCTS_DIR}/RxDataSources/RxDataSources.framework",
				"${BUILT_PRODUCTS_DIR}/RxGesture/RxGesture.framework",
				"${BUILT_PRODUCTS_DIR}/RxKeyboard/RxKeyboard.framework",
				"${BUILT_PRODUCTS_DIR}/RxRelay/RxRelay.framework",
				"${BUILT_PRODUCTS_DIR}/RxSwift/RxSwift.framework",
				"${BUILT_PRODUCTS_DIR}/SGCodeTextField/SGCodeTextField.framework",
				"${BUILT_PRODUCTS_DIR}/SnapKit/SnapKit.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftProtobuf/SwiftProtobuf.framework",
				"${BUILT_PRODUCTS_DIR}/ZLPhotoBrowser/ZLPhotoBrowser.framework",
				"${BUILT_PRODUCTS_DIR}/lottie-ios/Lottie.framework",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/LiveKitWebRTC/LiveKitWebRTC.framework/LiveKitWebRTC",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Alamofire.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ChatLayout.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DifferenceKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Differentiator.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManagerSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/InputBarAccessoryView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/JXSegmentedView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Kingfisher.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Lantern.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/LiveKitClient.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Localize_Swift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Logging.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MJExtension.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MJRefresh.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MMBAlertsPickers.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ProgressHUD.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxCocoa.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxDataSources.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxGesture.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxKeyboard.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxRelay.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SGCodeTextField.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SnapKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftProtobuf.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ZLPhotoBrowser.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Lottie.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/LiveKitWebRTC.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-OpenIMSDKUIKit_Example/Pods-OpenIMSDKUIKit_Example-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9DEED09E9EDFC150DA989F99 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OpenIMSDKUIKit_Example/Pods-OpenIMSDKUIKit_Example-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/OUICalling/OIMUICalling.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/OUICore/OIMUIResource.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/OUICore/OIMUIEmoji.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/OIMUICalling.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/OIMUIResource.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/OIMUIEmoji.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-OpenIMSDKUIKit_Example/Pods-OpenIMSDKUIKit_Example-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B0543E0AF23A91CC376A2734 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-OpenIMSDKUIKit_Example-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DCBD4C0227F07EE7003AFE6A /* Run Lint */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Run Lint";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# 格式自动调整\nif which swiftlint >/dev/null; then\nswiftlint autocorrect\necho \"格式调整完成\"\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		170A2B3228F29C5000CA68EA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				170A2B3928F29C5000CA68EA /* NotificationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		607FACCC1AFB9204008FA782 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0EB4A8332CC0A84300B7C494 /* CameraViewController.swift in Sources */,
				0E24585B2D9E2192008C748A /* IntentFeedCell.swift in Sources */,
				DC0B7DF4282DFA23003896C1 /* UnderlineTextField.swift in Sources */,
				1790EE6E2921EAFC00C4024A /* CheckBoxTextTableViewCell.swift in Sources */,
				1790EE6C2921EAFC00C4024A /* BlockedListViewController.swift in Sources */,
				DC0B7DF6282DFA85003896C1 /* DemoUI.swift in Sources */,
				0EA4F2C92DE057F7000F4B33 /* PhoneRegisterViewController.swift in Sources */,
				17C4BBAA28CA1220003782EF /* Array+deduplicate.swift in Sources */,
				1790EE7A2921EAFC00C4024A /* MainTabViewController.swift in Sources */,
				17C370A22A4DA5EF00CCB044 /* String+Extension.swift in Sources */,
				607FACD61AFB9204008FA782 /* AppDelegate.swift in Sources */,
				1790EE792921EAFC00C4024A /* LoginViewController.swift in Sources */,
				0E7068CE2E39B90B0098CBEB /* ProgressHUD+Custom.swift in Sources */,
				1790EE762921EAFC00C4024A /* ConfigViewController.swift in Sources */,
				17BBFAC02925C6330025B686 /* InputAccountViewController.swift in Sources */,
				1790EE6D2921EAFC00C4024A /* SettingTableViewController.swift in Sources */,
				0E4234142DD32A100033D520 /* EmailLoginViewController.swift in Sources */,
				0E4234152DD32A100033D520 /* EmailRegisterViewController.swift in Sources */,
				0E4234162DD32A100033D520 /* EmailForgotPasswordViewController.swift in Sources */,
				1790EE6F2921EAFC00C4024A /* SettingViewModel.swift in Sources */,
				17BBFAC42925D4C10025B686 /* CountDownTimerButton.swift in Sources */,
				1790EE722921EAFC00C4024A /* MineViewModel.swift in Sources */,
				0E22FE6F2D641ED200440976 /* IntentFeedViewController.swift in Sources */,
				0E22FE702D641ED200440976 /* FollowService.swift in Sources */,
				0E2458592D9D8A25008C748A /* FeedDetailViewController.swift in Sources */,
				0E22FE722D641ED200440976 /* IntentFeedModel.swift in Sources */,
				0E22FE732D641ED200440976 /* FollowModel.swift in Sources */,
				1790EE742921EAFC00C4024A /* MineViewController.swift in Sources */,
				0E8216A12D194CCD003725CB /* PublishIntentViewController.swift in Sources */,
				0E4234102DD1942E0033D520 /* LocalizationManager.swift in Sources */,
				1790EE812922294600C4024A /* AccountViewModel.swift in Sources */,
				17BBFAC22925CDBB0025B686 /* InputCodeViewController.swift in Sources */,
				1790EE712921EAFC00C4024A /* MineOptionTableViewCell.swift in Sources */,
				1780764A29260BF50024399B /* InputPasswordViewController.swift in Sources */,
				0EB679132DCC4D56005BEF43 /* LanguageSettingViewController.swift in Sources */,
				1790EE7F2922220D00C4024A /* ProfileTableViewController.swift in Sources */,
				DC0B7DF2282DFA09003896C1 /* UnderlineButton.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		170A2B3C28F29C5000CA68EA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 170A2B3528F29C5000CA68EA /* NotificationService */;
			targetProxy = 170A2B3B28F29C5000CA68EA /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		607FACD91AFB9204008FA782 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				607FACDA1AFB9204008FA782 /* Base */,
				DC05D4CF283E153400CC3974 /* zh-Hans */,
				DC05D4D0283E156300CC3974 /* en */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		607FACDE1AFB9204008FA782 /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				607FACDF1AFB9204008FA782 /* Base */,
				DC05D4D1283E156300CC3974 /* en */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
		DCE04173283E5BD30095874E /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				DCE04172283E5BD30095874E /* en */,
				DCE04174283E5BD70095874E /* zh-Hans */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		170A2B3F28F29C5000CA68EA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 330000;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = H73T6ANG2B;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2022 rentsoft. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				MARKETING_VERSION = 3.3.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = io.openim.ios.demoo123.newspace.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		170A2B4028F29C5000CA68EA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 330000;
				DEVELOPMENT_TEAM = H73T6ANG2B;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2022 rentsoft. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				MARKETING_VERSION = 3.3.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = io.openim.ios.dapp123.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		607FACED1AFB9204008FA782 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_SWIFT_FLAGS = "";
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		607FACEE1AFB9204008FA782 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_SWIFT_FLAGS = "";
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		607FACF01AFB9204008FA782 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BAF9BF6ADBCA82ACA78EB9E4 /* Pods-OpenIMSDKUIKit_Example.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				APPLICATION_EXTENSION_API_ONLY = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = OpenIMSDKUIKit_Example.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = H73T6ANG2B;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = OpenIMSDKUIKit/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 3.8.1;
				MODULE_NAME = ExampleApp;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = io.openim.ios.demoo123.newspace;
				PRODUCT_NAME = Chill;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_OBJC_BRIDGING_HEADER = "OpenIMSDKUIKit/OpenIMSDKUIKit_Example-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		607FACF11AFB9204008FA782 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 742F25766271FC0EB630862F /* Pods-OpenIMSDKUIKit_Example.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				APPLICATION_EXTENSION_API_ONLY = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = OpenIMSDKUIKit_Example.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = H73T6ANG2B;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
				);
				INFOPLIST_FILE = OpenIMSDKUIKit/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 3.8.1;
				MODULE_NAME = ExampleApp;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = io.openim.ios.dapp123;
				PRODUCT_NAME = Chill;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_OBJC_BRIDGING_HEADER = "OpenIMSDKUIKit/OpenIMSDKUIKit_Example-Bridging-Header.h";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		170A2B4128F29C5000CA68EA /* Build configuration list for PBXNativeTarget "NotificationService" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				170A2B3F28F29C5000CA68EA /* Debug */,
				170A2B4028F29C5000CA68EA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		607FACCB1AFB9204008FA782 /* Build configuration list for PBXProject "OpenIMSDKUIKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				607FACED1AFB9204008FA782 /* Debug */,
				607FACEE1AFB9204008FA782 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		607FACEF1AFB9204008FA782 /* Build configuration list for PBXNativeTarget "OpenIMSDKUIKit_Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				607FACF01AFB9204008FA782 /* Debug */,
				607FACF11AFB9204008FA782 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 607FACC81AFB9204008FA782 /* Project object */;
}
