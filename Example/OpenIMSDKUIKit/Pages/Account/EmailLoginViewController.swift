import UIKit
import RxSwift
import RxCocoa
import RxGesture
import SnapKit
import ProgressHUD

class EmailLoginViewController: UIViewController {
    
    private let _disposeBag = DisposeBag()
    private var isPasswordLogin = true // 默认使用密码登录
    private var scrollView: UIScrollView!
    private var activeTextField: UITextField?
    private var keyboardHeight: CGFloat = 0
    
    private let titleLabel: UILabel = {
        let v = UILabel()
        v.text = "邮箱登录".localized()
        v.font = UIFont.systemFont(ofSize: 24, weight: .semibold)
        v.textColor = DemoUI.color_0089FF
        v.isUserInteractionEnabled = true
        
        return v
    }()
    
    private lazy var emailSegment: UIButton = {
        let v = UIButton()
        v.setTitle("邮箱".localized(), for: .normal)
        v.setTitleColor(DemoUI.color_8E9AB0, for: .normal)
        v.titleLabel?.font = UIFont(name: "Avenir", size: 14) ?? DemoUI.smallFont
        
        return v
    }()
    
    lazy var registerButton: UIButton = {
        let t = UIButton(type: .system)
        t.setTitle("注册账号".localized(), for: .normal)
        t.titleLabel?.font = UIFont(name: "Avenir", size: 14) ?? .preferredFont(forTextStyle: .footnote)
        t.snp.makeConstraints { make in
            make.width.equalTo(70)
        }
        t.rx.tap.subscribe(onNext: { [weak self] _ in
            guard let sself = self else { return }
            sself.toRegister()
        }).disposed(by: _disposeBag)
        
        return t
    }()
    
    lazy var forgotButton: UIButton = {
        let t = UIButton(type: .system)
        t.setTitle("忘记密码".localized(), for: .normal)
        t.setTitleColor(DemoUI.color_8E9AB0, for: .normal)
        t.titleLabel?.font = UIFont(name: "Avenir", size: 12) ?? .systemFont(ofSize: 12)

        t.rx.tap.subscribe(onNext: { [weak self] _ in
            guard let sself = self else { return }
            sself.toForgotPasswordLogin()
        }).disposed(by: _disposeBag)
        
        return t
    }()
    
    lazy var switchLoginModeButton: UIButton = {
        let t = UIButton(type: .system)
        t.setTitle("验证码登录".localized(), for: .normal)
        t.setTitleColor(DemoUI.color_0089FF, for: .normal)
        t.titleLabel?.font = UIFont(name: "Avenir", size: 14) ?? .systemFont(ofSize: 14)
        
        t.rx.tap.subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            self.switchLoginMode()
        }).disposed(by: _disposeBag)
        
        return t
    }()
    
    lazy var loginBtn: UIButton = {
        let t = UIButton(type: .system)
        t.setTitle("登录".localized(), for: .normal)
        t.setTitleColor(.white, for: .normal)
        t.backgroundColor = DemoUI.color_0089FF
        t.layer.cornerRadius = DemoUI.cornerRadius
        
        return t
    }()
    
    private lazy var emailTextField: UITextField = {
        let v = UITextField()
        v.keyboardType = .emailAddress
        v.placeholder = "请输入邮箱地址".localized()
        v.clearButtonMode = .whileEditing
        v.text = AccountViewModel.perLoginAccount
        v.textColor = DemoUI.color_0C1C33
        v.font = UIFont(name: "Avenir", size: 17) ?? .systemFont(ofSize: 17)
        
        return v
    }()
    
    private lazy var passwordTextField: UITextField = {
        let v = UITextField()
        v.keyboardType = .asciiCapable
        v.placeholder = "请输入密码".localized()
        v.clearButtonMode = .whileEditing
        v.isSecureTextEntry = true
        v.textColor = DemoUI.color_0C1C33
        v.font = UIFont(name: "Avenir", size: 17) ?? .systemFont(ofSize: 17)
        
        return v
    }()
    
    private lazy var verificationCodeTextField: UITextField = {
        let v = UITextField()
        v.keyboardType = .numberPad
        v.placeholder = "请输入验证码".localized()
        v.clearButtonMode = .whileEditing
        v.textColor = DemoUI.color_0C1C33
        v.font = UIFont(name: "Avenir", size: 17) ?? .systemFont(ofSize: 17)
        
        return v
    }()
    
    private lazy var getVerificationCodeButton: UIButton = {
        let t = UIButton(type: .system)
        t.setTitle("获取验证码".localized(), for: .normal)
        t.setTitleColor(DemoUI.color_0089FF, for: .normal)
        t.titleLabel?.font = UIFont(name: "Avenir", size: 14) ?? .systemFont(ofSize: 14)
        t.snp.makeConstraints { make in
            make.width.equalTo(80)
        }
        
        t.rx.tap.subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            self.getVerificationCode()
        }).disposed(by: _disposeBag)
        
        return t
    }()
    
    var email: String? {
        return emailTextField.text?.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
    }
    
    var password: String? {
        return passwordTextField.text?.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
    }
    
    var verificationCode: String? {
        return verificationCodeTextField.text?.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        
        setupUI()
        setupBindings()
        setupKeyboardHandling()
        setupTextFieldDelegates()
        setupTapGesture()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        addKeyboardObservers()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        removeKeyboardObservers()
    }
    
    private func setupUI() {
        let bgImageView = UIImageView(image: UIImage(named: "login_bg"))
        bgImageView.frame = view.bounds
        view.addSubview(bgImageView)
        
        // 添加返回按钮
        let backButton = UIButton(type: .system)
        backButton.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        backButton.rx.tap.subscribe(onNext: { [weak self] in
            if let navigationController = self?.navigationController {
                navigationController.popViewController(animated: true)
            } else {
                self?.dismiss(animated: true)
            }
        }).disposed(by: _disposeBag)
        
        view.addSubview(backButton)
        backButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(24)
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(8)
        }
        
        // 添加标题
        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(backButton)
            make.top.equalTo(backButton.snp.bottom).offset(42)
        }
        
        // 创建滚动视图
        scrollView = UIScrollView()
        view.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        // 创建内容容器
        let contentView = UIView()
        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
            make.height.greaterThanOrEqualTo(view.snp.height).offset(-200).priority(.low)
        }
        
        // 邮箱输入框
        let emailStack = UIStackView(arrangedSubviews: [emailSegment, emailTextField])
        emailStack.spacing = 8
        emailStack.alignment = .center
        emailStack.layer.cornerRadius = DemoUI.cornerRadius
        emailStack.layer.borderColor = DemoUI.color_E8EAEF.cgColor
        emailStack.layer.borderWidth = 1
        
        emailSegment.snp.makeConstraints { make in
            make.width.equalTo(60)
        }
        
        emailTextField.snp.makeConstraints { make in
            make.height.equalTo(42)
        }
        
        contentView.addSubview(emailStack)
        emailStack.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(32)
            make.top.equalToSuperview().offset(16)
        }
        
        // 密码输入框
        let passwordStack = UIStackView(arrangedSubviews: [passwordTextField])
        passwordStack.layer.cornerRadius = DemoUI.cornerRadius
        passwordStack.layer.borderColor = DemoUI.color_E8EAEF.cgColor
        passwordStack.layer.borderWidth = 1
        
        passwordTextField.snp.makeConstraints { make in
            make.height.equalTo(42)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        contentView.addSubview(passwordStack)
        passwordStack.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(32)
            make.top.equalTo(emailStack.snp.bottom).offset(16)
        }
        
        // 验证码输入框和获取按钮
        let verificationCodeStack = UIStackView(arrangedSubviews: [verificationCodeTextField, getVerificationCodeButton])
        verificationCodeStack.spacing = 8
        verificationCodeStack.alignment = .center
        verificationCodeStack.layer.cornerRadius = DemoUI.cornerRadius
        verificationCodeStack.layer.borderColor = DemoUI.color_E8EAEF.cgColor
        verificationCodeStack.layer.borderWidth = 1
        
        verificationCodeTextField.snp.makeConstraints { make in
            make.height.equalTo(42)
        }
        
        contentView.addSubview(verificationCodeStack)
        verificationCodeStack.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(32)
            make.top.equalTo(emailStack.snp.bottom).offset(16)
        }
        
        // 切换登录模式按钮
        contentView.addSubview(switchLoginModeButton)
        switchLoginModeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(32)
            make.top.equalTo(passwordStack.snp.bottom).offset(16)
        }
        
        // 忘记密码按钮
        contentView.addSubview(forgotButton)
        forgotButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(32)
            make.centerY.equalTo(switchLoginModeButton)
        }
        
        // 登录按钮
        contentView.addSubview(loginBtn)
        loginBtn.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(32)
            make.top.equalTo(switchLoginModeButton.snp.bottom).offset(40)
            make.height.equalTo(44)
        }
        
        // 注册按钮
        contentView.addSubview(registerButton)
        registerButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(loginBtn.snp.bottom).offset(16)
            make.bottom.equalToSuperview().offset(-24)
        }
        
        // 初始状态设置
        updateUIForLoginMode()
    }
    
    private func setupBindings() {
        loginBtn.rx.tap.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.login()
        }).disposed(by: _disposeBag)
    }
    
    private func updateUIForLoginMode() {
        if isPasswordLogin {
            passwordTextField.isHidden = false
            verificationCodeTextField.isHidden = true
            getVerificationCodeButton.isHidden = true
            switchLoginModeButton.setTitle("验证码登录".localized(), for: .normal)
            forgotButton.isHidden = false
        } else {
            passwordTextField.isHidden = true
            verificationCodeTextField.isHidden = false
            getVerificationCodeButton.isHidden = false
            switchLoginModeButton.setTitle("密码登录".localized(), for: .normal)
            forgotButton.isHidden = true
        }
    }
    
    private func switchLoginMode() {
        isPasswordLogin = !isPasswordLogin
        updateUIForLoginMode()
    }
    
    private func login() {
        guard let email = self.email, !email.isEmpty else {
            ProgressHUD.error("请输入邮箱地址".localized())
            return
        }
        
        // 验证邮箱格式
        if !isValidEmail(email) {
            ProgressHUD.error("邮箱格式不正确".localized())
            return
        }
        
        if isPasswordLogin {
            // 密码登录
            guard let password = self.password, password.count >= 6 else {
                ProgressHUD.showBannerError("密码不能少于6位".localized())
                return
            }

            ProgressHUD.showLoginLoading()
            AccountViewModel.loginWithEmail(email: email, password: password) { [weak self] (errCode, errMsg) in
                if errMsg != nil {
                    ProgressHUD.showLoginError(errMsg!)
                } else {
                    ProgressHUD.showLoginSuccess("登录成功".localized())
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        self?.dismiss(animated: true)
                    }
                }
            }
        } else {
            // 验证码登录
            guard let code = self.verificationCode, !code.isEmpty else {
                ProgressHUD.showBannerError("请输入验证码".localized())
                return
            }

            ProgressHUD.showLoginLoading()
            AccountViewModel.loginWithEmail(email: email, verificationCode: code) { [weak self] (errCode, errMsg) in
                if errMsg != nil {
                    ProgressHUD.showLoginError(errMsg!)
                } else {
                    ProgressHUD.showLoginSuccess("登录成功".localized())
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        self?.dismiss(animated: true)
                    }
                }
            }
        }
    }
    
    private func getVerificationCode() {
        guard let email = self.email, !email.isEmpty else {
            ProgressHUD.showBannerError("请输入邮箱地址".localized())
            return
        }

        // 验证邮箱格式
        if !isValidEmail(email) {
            ProgressHUD.showBannerError("邮箱格式不正确".localized())
            return
        }

        ProgressHUD.showCodeSending()
        AccountViewModel.requestCodeForEmail(email: email, usedFor: .login) { (errCode, errMsg) in
            if errMsg != nil {
                ProgressHUD.showLoginError(errMsg!)
            } else {
                ProgressHUD.showCodeSent("验证码已发送".localized())
                // 可以在这里添加倒计时功能
            }
        }
    }
    
    private func toRegister() {
        let vc = EmailRegisterViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    private func toForgotPasswordLogin() {
        let vc = EmailForgotPasswordViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    // MARK: - Keyboard Handling
    
    private func setupKeyboardHandling() {
        // Configure scrollView content insets for keyboard
        scrollView.contentInsetAdjustmentBehavior = .automatic
        scrollView.keyboardDismissMode = .interactive
    }
    
    private func setupTextFieldDelegates() {
        emailTextField.delegate = self
        passwordTextField.delegate = self
        verificationCodeTextField.delegate = self
    }
    
    private func setupTapGesture() {
        // Add tap gesture to dismiss keyboard when tapping outside text fields
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tapGesture.cancelsTouchesInView = false
        view.addGestureRecognizer(tapGesture)
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    private func addKeyboardObservers() {
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHide), name: UIResponder.keyboardWillHideNotification, object: nil)
    }
    
    private func removeKeyboardObservers() {
        NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
    }
    
    @objc private func keyboardWillShow(notification: NSNotification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }
        
        keyboardHeight = keyboardFrame.height
        
        // Adjust scrollView contentInset
        let contentInsets = UIEdgeInsets(top: 0, left: 0, bottom: keyboardHeight, right: 0)
        scrollView.contentInset = contentInsets
        scrollView.scrollIndicatorInsets = contentInsets
        
        // Scroll to active text field if it's being covered by keyboard
        if let activeTextField = activeTextField {
            let convertedFrame = activeTextField.convert(activeTextField.bounds, to: scrollView)
            var visibleRect = scrollView.bounds
            visibleRect.size.height -= keyboardHeight
            
            if !visibleRect.contains(convertedFrame.origin) {
                scrollView.scrollRectToVisible(convertedFrame, animated: true)
            }
        }
    }
    
    @objc private func keyboardWillHide(notification: NSNotification) {
        // Reset scrollView contentInset
        let contentInsets = UIEdgeInsets.zero
        scrollView.contentInset = contentInsets
        scrollView.scrollIndicatorInsets = contentInsets
    }
}

// MARK: - UITextFieldDelegate
extension EmailLoginViewController: UITextFieldDelegate {
    func textFieldDidBeginEditing(_ textField: UITextField) {
        activeTextField = textField
        
        // If keyboard is already shown, scroll to the active text field
        if keyboardHeight > 0 {
            let convertedFrame = textField.convert(textField.bounds, to: scrollView)
            var visibleRect = scrollView.bounds
            visibleRect.size.height -= keyboardHeight
            
            if !visibleRect.contains(convertedFrame.origin) {
                scrollView.scrollRectToVisible(convertedFrame, animated: true)
            }
        }
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        activeTextField = nil
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        // Move to next text field or dismiss keyboard if it's the last one
        if textField == emailTextField {
            if isPasswordLogin {
                passwordTextField.becomeFirstResponder()
            } else {
                verificationCodeTextField.becomeFirstResponder()
            }
        } else {
            textField.resignFirstResponder()
            login()
        }
        return true
    }
} 