import OUIIM
import OUICore
import OpenIMSDK
import RxSwift
import RxCocoa
import ProgressHUD
import Localize_Swift
import MJExtension
//import GTSDK
import UIKit
import AVFoundation
import Photos
import OUICore
import OpenIMCore

class MainTabViewController: UITabBarController,UITextFieldDelegate, UINavigationControllerDelegate, UITabBarControllerDelegate {
    // 添加静态实例
    static var shared: MainTabViewController?
    
    private let _disposeBag = DisposeBag()
    private let inputContainerView = UIView()
    private let userNameDropdown = UIButton()
    private let inputField = UITextField()
    private let timeFilterDropdown = UIButton()
    private var inputContainerBottomConstraint: NSLayoutConstraint!
    private var tabBarBottomConstraint: NSLayoutConstraint!
    private let buttonContainerView = UIView() // 按钮容器视图
    private let atButton = UIButton() // 添加 @ 按钮
    private var messageTabIndex: Int?
    private var isReturningFromCamera = false
    // 添加一个标志来防止重复登录检查
    private var hasCheckedLoginStatus = false
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // 保存静态实例
        MainTabViewController.shared = self

        // 显示启动屏延迟
        showLaunchScreenDelay()

        setupViewControllers()

        // 确保主视图背景色是白色
        self.view.backgroundColor = .white

        setupTabBarItemAppearance()

        setupInputView()

        self.view.addSubview(inputContainerView)

        // 初始隐藏输入框，等启动屏结束后再显示（但不设置alpha=0，这样启动屏结束后可以立即显示）
        inputContainerView.isHidden = true
        print("Navigation Controller: \(self.navigationController)") // 打印当前的 navigationController
        print("View Controller: \(self)") // 打印当前视图控制器

        tabBarBottomConstraint = tabBar.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: 0)
        tabBarBottomConstraint.isActive = true // 激活底部约束
        
        self.tabBar.isTranslucent = false
        self.tabBar.backgroundColor = .white
        
        self.tabBar.layer.shadowColor = UIColor.black.cgColor
        self.tabBar.layer.shadowOpacity = 0.08
        self.tabBar.layer.shadowOffset = CGSize.init(width: 0, height: 0)
        self.tabBar.layer.shadowRadius = 5
        
        self.tabBar.backgroundImage = UIImage.init()
        self.tabBar.shadowImage = UIImage.init()
        
        // 延迟所有可能触发加载状态的操作，等启动屏结束后再执行
        DispatchQueue.main.asyncAfter(deadline: .now() + 4.0) { [weak self] in
            self?.initializeAfterLaunchScreen()
        }
        
        // 添加通知监听
        NotificationCenter.default.addObserver(self, selector: #selector(presentLoginController), name: .init("logout"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(presentLoginController), name: .init("showLogin"), object: nil)
        
        // 只为包 ChatListViewController 的导航控制器设置代理
        if let navController = viewControllers?.first as? UINavigationController,
           navController.viewControllers.first is ChatListViewController {
            navController.delegate = self
        }

        // 设置选中项的颜色为黑色
        self.tabBar.tintColor = UIColor(red: 0/255, green: 195/255, blue: 102/255, alpha: 1) // 更亮的绿色

        self.delegate = self
        preloadInputView() // 预加载输入视图
        setupMessageTabIndex()
        
        // 添加TabBar长按手势
        setupTabBarLongPressGesture()

        // 添加通知观察者
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePresentChatViewController(_:)),
            name: .init("PresentChatViewController"),
            object: nil
        )

        // 添加语言变更通知监听
        NotificationCenter.default.addObserver(self,
                                             selector: #selector(handleLanguageChange),
                                             name: NSNotification.Name("AppLanguageChanged"),
                                             object: nil)
        
        NotificationCenter.default.addObserver(self,
                                             selector: #selector(handleLanguageChange),
                                             name: NSNotification.Name("RefreshUIForLanguageChange"),
                                             object: nil)

        // 初始化后立即使用LocalizationManager刷新TabBar标题
        LocalizationManager.shared.refreshTabBarTitles()
    }

    @objc private func handlePresentChatViewController(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let viewController = userInfo["viewController"] as? UIViewController else {
            return
        }
        
        // 确保在主线程执行
        DispatchQueue.main.async {
            // 获取当前选中的导航控制器
            if let selectedNav = self.selectedViewController as? UINavigationController {
                selectedNav.pushViewController(viewController, animated: true)
            }
        }
    }

    private func setupInputView() {
        // 修改输入容器视图的背景色
        inputContainerView.backgroundColor = .white
        inputContainerView.translatesAutoresizingMaskIntoConstraints = false
        
        // 确保主视图背景色也是白色
        self.view.backgroundColor = .white
        
        self.view.addSubview(inputContainerView)

        // 修改输入框的样式
        inputField.layer.cornerRadius = 22
        inputField.clipsToBounds = true
        inputField.backgroundColor = .white
        inputField.borderStyle = .none
        inputField.layer.borderWidth = 1.0
        inputField.layer.borderColor = UIColor.black.cgColor
        inputField.isUserInteractionEnabled = true
        inputField.delegate = self
        inputField.translatesAutoresizingMaskIntoConstraints = false

        // 创建一个居中的 NSAttributedString 作为占位文本
        let placeholderText = "输入你的意图".localized()
        print("📱 设置输入框占位文本 - 当前语言: \(Localize.currentLanguage()), 本地化结果: '\(placeholderText)'")
        
        let attributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor.lightGray,
            .font: UIFont(name: "Avenir", size: 15) ?? UIFont.systemFont(ofSize: 16),
            .paragraphStyle: {
                let style = NSMutableParagraphStyle()
                style.alignment = .center
                return style
            }()
        ]
        let attributedPlaceholder = NSAttributedString(string: placeholderText, attributes: attributes)
        inputField.attributedPlaceholder = attributedPlaceholder

        inputContainerView.addSubview(inputField)

        // 修改约束，移除 @ 按钮相关约束
        NSLayoutConstraint.activate([
            inputContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            inputContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            inputContainerView.heightAnchor.constraint(equalToConstant: 60),

            inputField.leadingAnchor.constraint(equalTo: inputContainerView.leadingAnchor, constant: 8),
            inputField.trailingAnchor.constraint(equalTo: inputContainerView.trailingAnchor, constant: -8),
            inputField.topAnchor.constraint(equalTo: inputContainerView.topAnchor, constant: 5),
            inputField.bottomAnchor.constraint(equalTo: inputContainerView.bottomAnchor, constant: -5),
        ])
        
        // 初始设置底部约束
        inputContainerBottomConstraint = inputContainerView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        inputContainerBottomConstraint.isActive = true

        let tapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(inputFieldTapped))
        inputField.addGestureRecognizer(tapGestureRecognizer)
    }

    @objc private func inputFieldTapped() {
        let publishVC: UIViewController = PublishIntentViewController()
        if let intentVC = publishVC as? PublishIntentViewController {
            intentVC.targetUserId = "7431589925" // 这里需要传入当前对话的用户ID
        }
        let navController = UINavigationController(rootViewController: publishVC)
        navController.modalPresentationStyle = UIModalPresentationStyle.fullScreen
        present(navController, animated: true)
    }

    @objc private func showFriendList() {
        let friendListVC = FriendListViewController()
        friendListVC.showsBackButton = false  // 设置不显示返回按
        friendListVC.selectCallBack = { [weak self] user in
            self?.inputField.text = "@\(user.nickname) "
            self?.dismissKeyboard()
            self?.dismiss(animated: true, completion: nil)
        }
        
        let nav = UINavigationController(rootViewController: friendListVC)
        self.present(nav, animated: true, completion: nil)
    }
    
    override func tabBar(_ tabBar: UITabBar, didSelect item: UITabBarItem) {
        let selectedIndex = tabBar.items?.firstIndex(of: item)
        
        if selectedIndex == messageTabIndex {
            // 如果选择的是消息标签页，确保输入框可见
            DispatchQueue.main.async { [weak self] in
                self?.inputContainerView.isHidden = false
                self?.updateInputContainerPosition()
            }
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    @objc private func dismissKeyboard() {
        inputField.resignFirstResponder() // 隐藏键盘
        inputContainerBottomConstraint.constant = -8 // 恢复输入框位置

        UIView.animate(withDuration: 0.3) {
            self.view.layoutIfNeeded() // 更新布局
        }
    }
   
    // UITextFieldDelegate 方法
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder() // 点击回车时收起键盘
        return true
    }

    @objc private func presentLoginController() {
        print("DEBUG: Presenting login controller")

        // 清除旧的登录状态
        AccountViewModel.saveUser(uid: nil, imToken: nil, chatToken: nil)
        AccountViewModel.isFromRegistration = false

        // 重置登录检查标志，允许下次重新检查
        hasCheckedLoginStatus = false

        // 重置选中的标签页到第一个
        self.selectedIndex = 0

        // 清除绑定
        pushBindAlias(false)
        
        // 确保在主线程执行
        DispatchQueue.main.async {
            // 如果当前已经在显示登录页面，不要重复显示
            if let presentedVC = self.presentedViewController as? LoginViewController {
                print("DEBUG: Login controller already presented")
                return
            }
            
            // 创建并显示登录页面
            let vc = LoginViewController()
            vc.modalPresentationStyle = .fullScreen
            
            print("DEBUG: Presenting new login controller")
            self.present(vc, animated: true)
        }
    }
    
    @objc private func showEmailLogin() {
        let vc = EmailLoginViewController()
        let navController = UINavigationController(rootViewController: vc)
        navController.modalPresentationStyle = .fullScreen
        navController.setNavigationBarHidden(true, animated: false) // 隐藏导航栏，使用自定义返回按钮
        self.present(navController, animated: true)
                }
    
    @objc private func dismissEmailLogin() {
        self.dismiss(animated: true)
    }
    
    func pushBindAlias(_ bind: Bool = true) {
        if let userID = AccountViewModel.userID {
//            bind ? GeTuiSdk.bindAlias(userID, andSequenceNum: "im") : GeTuiSdk.unbindAlias(userID, andSequenceNum: "im", andIsSelf: true)
        }
    }
    
    func navigationController(_ navigationController: UINavigationController, willShow viewController: UIViewController, animated: Bool) {
        if viewController is ChatListViewController && navigationController.viewControllers.count == 1 {
            inputContainerView.isHidden = false
            updateInputContainerPosition()
        } else {
            inputContainerView.isHidden = true
        }
    }
    
    func navigationController(_ navigationController: UINavigationController, didShow viewController: UIViewController, animated: Bool) {
        if viewController is ChatListViewController && navigationController.viewControllers.count == 1 {
            inputContainerView.isHidden = false
            updateInputContainerPosition()
        } else {
            inputContainerView.isHidden = true
        }
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        if selectedIndex == messageTabIndex && !inputContainerView.isHidden {
            updateInputContainerPosition()
        }
    }

    private func updateInputContainerPosition() {
        guard !inputContainerView.isHidden else { return }
        
        let tabBarTop = tabBar.frame.minY
        let inputContainerBottom = view.frame.height - tabBarTop
        
        let spacing: CGFloat = 1
        inputContainerBottomConstraint.constant = -inputContainerBottom + spacing
        
        view.layoutIfNeeded()
    }

    // UITabBarControllerDelegate 方法
    func tabBarController(_ tabBarController: UITabBarController, shouldSelect viewController: UIViewController) -> Bool {
        let selectedIndex = tabBarController.viewControllers?.firstIndex(of: viewController)
        
        // 检查是否是重复点击朋友标签页
        if let navController = viewController as? UINavigationController,
           navController.viewControllers.first is ContactsViewController,
           tabBarController.selectedViewController == viewController {
            
            // 如果是重复点击朋友标签页，不执行标签切换，而是直接在当前导航栈上操作
            DispatchQueue.main.async {
                // 先将导航栈重置到根视图控制器
                navController.popToRootViewController(animated: false)
                
                // 然后立即推送新的 FriendListViewController
                let friendVC = FriendListViewController()
                friendVC.isFromTabBar = true
                friendVC.hidesBottomBarWhenPushed = false
                navController.pushViewController(friendVC, animated: false)
            }
            
            // 返回 false 阻止标签切换，避免白屏闪烁
            return false
        }
        
        if selectedIndex == messageTabIndex {
            // 如果选择的是消息标签页，确保输入框可见
            DispatchQueue.main.async { [weak self] in
                self?.inputContainerView.isHidden = false
                self?.updateInputContainerPosition()
            }
        } else {
            // 如果选择的不是消息标签页，隐藏输入框
            inputContainerView.isHidden = true
        }
        
        // 处理拍照 tabbar
        if viewController is UINavigationController,
           let cameraVC = (viewController as? UINavigationController)?.topViewController as? CameraViewController {
            isReturningFromCamera = true
            let fullScreenCameraVC = CameraViewController()
            fullScreenCameraVC.modalPresentationStyle = .fullScreen
            fullScreenCameraVC.modalTransitionStyle = .crossDissolve
            present(fullScreenCameraVC, animated: true, completion: nil)
            return false
        }
        
        return true
    }

    // 添加这个新方法
    func handleCameraViewControllerDismissal() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            if self.selectedIndex == self.messageTabIndex {
                self.inputContainerView.isHidden = false
                self.updateInputContainerPosition()
            }
        }
    }

    private func preloadInputView() {
        if inputContainerView.superview == nil {
            view.addSubview(inputContainerView)
            setupInputView()
        }
        inputContainerView.isHidden = true
    }

    // 在 viewDidLoad 方法中，在设置 viewControllers 之前添加这个函数

    private func setupTabBarItemAppearance() {
        // 普通状态
        let normalAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont(name: "Avenir", size: 12) ?? UIFont.systemFont(ofSize: 12),
            .foregroundColor: UIColor.gray  // 未选中状态的颜色
        ]
        
        // 选中状态
        let selectedAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont(name: "Avenir-Medium", size: 12) ?? UIFont.systemFont(ofSize: 12, weight: .medium),
            .foregroundColor: UIColor.black
        ]
        
        // 为所有 tab items 设置默认外观
        let appearance = UITabBarItem.appearance()
        appearance.setTitleTextAttributes(normalAttributes, for: .normal)
        appearance.setTitleTextAttributes(selectedAttributes, for: .selected)
        
        // 设置选中项的颜色为黑色
        self.tabBar.tintColor = .black
    }

    private func setupMessageTabIndex() {
        messageTabIndex = viewControllers?.firstIndex(where: { 
            ($0 as? UINavigationController)?.viewControllers.first is ChatListViewController 
        })
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 确保视图背景色是白色
        self.view.backgroundColor = .white
        inputContainerView.backgroundColor = .white
        
        if isReturningFromCamera {
            showInputContainer()
            isReturningFromCamera = false
        }
        // 每次视图出现时重新设置 tabBar 样式
        setupTabBarAppearance()

        // 确保每次显示时都刷新TabBar标题
        LocalizationManager.shared.refreshTabBarTitles()
    }

    private func showInputContainer() {
        inputContainerView.isHidden = false
        updateInputContainerPosition()
    }

    // 添加公共方法用于隐藏输入框
    public func hideInputContainerView() {
        inputContainerView.isHidden = true
        inputContainerView.alpha = 0
        view.endEditing(true)
    }

    // 添加公共方法用于显示输入框
    public func showInputContainerView() {
        inputContainerView.isHidden = false
        inputContainerView.alpha = 1  // 立即显示，不需要动画
        updateInputContainerPosition()
    }

    // 修改 tabBar 的选择处理
    public func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        // 检查是否是朋友标签页
        if let navigationController = viewController as? UINavigationController,
           navigationController.viewControllers.first is ContactsViewController {
            
            // 确保视图背景色是白色，避免透明背景导致的闪烁
            navigationController.view.backgroundColor = .white
            
            // 如果导航栈中有多个视图控制器，并且顶部不是 FriendListViewController
            if navigationController.viewControllers.count > 1,
               !(navigationController.topViewController is FriendListViewController) {
                // 返回到根视图控制器
                navigationController.popToRootViewController(animated: false)
                
                // 重新创建并推送 FriendListViewController
                let friendVC = FriendListViewController()
                friendVC.isFromTabBar = true
                friendVC.hidesBottomBarWhenPushed = false
                navigationController.pushViewController(friendVC, animated: false)
            }
            
            // 如果导航栈中只有根视图控制器，确保推送 FriendListViewController
            if navigationController.viewControllers.count == 1 {
                // 检查是否已经有 FriendListViewController
                if !(navigationController.viewControllers.contains(where: { $0 is FriendListViewController })) {
                    let friendVC = FriendListViewController()
                    friendVC.isFromTabBar = true
                    friendVC.hidesBottomBarWhenPushed = false
                    navigationController.pushViewController(friendVC, animated: false)
                }
            }
        }
        
        // 根据选中的标签页决定是否显示输入框
        if selectedIndex == messageTabIndex {
            showInputContainerView()
        } else {
            hideInputContainerView()
        }
    }

    // 添加辅助方法来创建统一的 TabBarItem
    private func createTabBarItem(title: String, normalImage: String, selectedImage: String) -> UITabBarItem {
        let item = UITabBarItem(title: title, image: nil, selectedImage: nil)
        
        // 设置普通状态的图标
        if let image = UIImage(named: normalImage) {
            let resizedImage = image.resized(to: CGSize(width: 26, height: 26))
            item.image = resizedImage.withRenderingMode(.alwaysOriginal)
        }
        
        // 设置选中状态的图标
        if let selectedImage = UIImage(named: selectedImage) {
            let resizedSelectedImage = selectedImage.resized(to: CGSize(width: 26, height: 26))
            item.selectedImage = resizedSelectedImage.withRenderingMode(.alwaysOriginal)
        }
        
        // 为每个 item 单独设置字体属性
        let normalAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont(name: "Avenir", size: 12) ?? UIFont.systemFont(ofSize: 12),
            .foregroundColor: UIColor.gray
        ]
        
        let selectedAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont(name: "Avenir-Medium", size: 12) ?? UIFont.systemFont(ofSize: 12, weight: .medium),
            .foregroundColor: UIColor.black
        ]
        
        item.setTitleTextAttributes(normalAttributes, for: .normal)
        item.setTitleTextAttributes(selectedAttributes, for: .selected)
        
        return item
    }

    private func setupTabBarAppearance() {
        // 设置选中项的图标和文字颜色为黑色
        self.tabBar.tintColor = .black
        
        // 为所有 items 重新设置属性
        viewControllers?.forEach { viewController in
            let normalAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont(name: "Avenir", size: 12) ?? UIFont.systemFont(ofSize: 12),
                .foregroundColor: UIColor.gray
            ]
            
            let selectedAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont(name: "Avenir-Medium", size: 12) ?? UIFont.systemFont(ofSize: 12, weight: .medium),
                .foregroundColor: UIColor.black
            ]
            
            viewController.tabBarItem.setTitleTextAttributes(normalAttributes, for: .normal)
            viewController.tabBarItem.setTitleTextAttributes(selectedAttributes, for: .selected)
        }
    }

    private func setupViewControllers() {
        var controllers: [UIViewController] = []
        
        // 消息标签页
        let chatNav = UINavigationController(rootViewController: ChatListViewController())
        chatNav.tabBarItem.title = "消息".localized()
        if let image = UIImage(named: "tab_home_icon_normal") {
            let resizedImage = image.resized(to: CGSize(width: 24, height: 24))
            chatNav.tabBarItem.image = resizedImage.withRenderingMode(.alwaysOriginal)
        }
        if let selectedImage = UIImage(named: "tab_home_icon_selected") {
            let resizedSelectedImage = selectedImage.resized(to: CGSize(width: 24, height: 24))
            chatNav.tabBarItem.selectedImage = resizedSelectedImage.withRenderingMode(.alwaysOriginal)
        }
        controllers.append(chatNav)
        
        // Intent Feed标签页
        let feedVC = IntentFeedViewController()
        let feedNav = UINavigationController(rootViewController: feedVC)
        feedNav.tabBarItem.title = "意图动态".localized()
        if let image = UIImage(named: "tab_feed") {
            let resizedImage = image.resized(to: CGSize(width: 24, height: 24))
            feedNav.tabBarItem.image = resizedImage.withRenderingMode(.alwaysOriginal)
        }
        if let selectedImage = UIImage(named: "tab_feed_selected") {
            let resizedSelectedImage = selectedImage.resized(to: CGSize(width: 24, height: 24))
            feedNav.tabBarItem.selectedImage = resizedSelectedImage.withRenderingMode(.alwaysOriginal)
        }
        controllers.append(feedNav)
        
        /* 暂时注释掉拍照标签页
        // 拍照标签页
        let cameraVC = CameraViewController()
        let cameraNav = UINavigationController(rootViewController: cameraVC)
        cameraNav.tabBarItem.title = "拍照"
        if let image = UIImage(named: "tab_camera_icon_normal") {
            let resizedImage = image.resized(to: CGSize(width: 24, height: 24))
            cameraNav.tabBarItem.image = resizedImage.withRenderingMode(.alwaysOriginal)
        }
        if let selectedImage = UIImage(named: "tab_camera_icon_selected") {
            let resizedSelectedImage = selectedImage.resized(to: CGSize(width: 24, height: 24))
            cameraNav.tabBarItem.selectedImage = resizedSelectedImage.withRenderingMode(.alwaysOriginal)
        }
        controllers.append(cameraNav)
        */
        
        // 朋友标签页
        let contactVC = ContactsViewController()
        contactVC.viewModel.dataSource = self
        let contactNav = UINavigationController(rootViewController: contactVC)
        contactNav.tabBarItem.title = "朋友".localized()
        if let image = UIImage(named: "tab_contact_icon_normal") {
            let resizedImage = image.resized(to: CGSize(width: 24, height: 24))
            contactNav.tabBarItem.image = resizedImage.withRenderingMode(.alwaysOriginal)
        }
        if let selectedImage = UIImage(named: "tab_contact_icon_selected") {
            let resizedSelectedImage = selectedImage.resized(to: CGSize(width: 24, height: 24))
            contactNav.tabBarItem.selectedImage = resizedSelectedImage.withRenderingMode(.alwaysOriginal)
        }
        controllers.append(contactNav)
        
        // 我的标签页
        let mineNav = UINavigationController(rootViewController: MineViewController())
        mineNav.tabBarItem.title = "我的".localized()
        if let image = UIImage(named: "tab_me_icon_normal") {
            let resizedImage = image.resized(to: CGSize(width: 24, height: 24))
            mineNav.tabBarItem.image = resizedImage.withRenderingMode(.alwaysOriginal)
        }
        if let selectedImage = UIImage(named: "tab_me_icon_selected") {
            let resizedSelectedImage = selectedImage.resized(to: CGSize(width: 24, height: 24))
            mineNav.tabBarItem.selectedImage = resizedSelectedImage.withRenderingMode(.alwaysOriginal)
        }
        controllers.append(mineNav)
        
        // 统一设置 TabBar 文字样式
        let normalAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 10),
            .foregroundColor: UIColor(hex: "#999999") // 使用浅灰色
        ]
        let selectedAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 10),
            .foregroundColor: UIColor.black
        ]
        
        UITabBarItem.appearance().setTitleTextAttributes(normalAttributes, for: .normal)
        UITabBarItem.appearance().setTitleTextAttributes(selectedAttributes, for: .selected)
        
        // 设置 TabBar 整体样式
        self.tabBar.backgroundColor = .white
        self.tabBar.isTranslucent = false
        
        self.viewControllers = controllers
    }

    // 添加长按手势设置方法
    private func setupTabBarLongPressGesture() {
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleTabBarLongPress(_:)))
        longPress.minimumPressDuration = 0.5 // 设置长按时间为0.5秒
        tabBar.addGestureRecognizer(longPress)
    }
    
    // 处理TabBar长按事件
    @objc private func handleTabBarLongPress(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began {
            // 获取长按位置
            let location = gesture.location(in: tabBar)
            
            // 检查是否在消息tab上长按
            if let items = tabBar.items,
               let messageIndex = items.firstIndex(where: { $0.title == "消息".localized() }) {
                
                let itemWidth = tabBar.bounds.width / CGFloat(items.count)
                let itemFrame = CGRect(x: itemWidth * CGFloat(messageIndex), y: 0, width: itemWidth, height: tabBar.bounds.height)
                
                if itemFrame.contains(location) {
                    // 找到ChatListViewController并触发UI风格切换
                    if let navController = viewControllers?[messageIndex] as? UINavigationController,
                       let chatListVC = navController.viewControllers.first as? ChatListViewController {
                        
                        // 使用反射在不暴露私有方法的情况下调用切换风格方法
                        if chatListVC.responds(to: Selector(("toggleUIStyle"))) {
                            chatListVC.perform(Selector(("toggleUIStyle")))
                        }
                    }
                }
            }
        }
    }

    @objc private func handleLanguageChange() {
        print("🌐 语言变更通知 - 当前语言: \(Localize.currentLanguage())")
        
        // 更新所有TabBar项的标题
        if let items = tabBar.items {
            items[0].title = "消息".localized()
            items[1].title = "Chill时刻".localized()
            items[2].title = "朋友".localized()
            items[3].title = "我的".localized()
        }
        
        // 更新输入框占位文本
        let placeholderText = "输入你的意图".localized()
        let attributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor.lightGray,
            .font: UIFont(name: "Avenir", size: 15) ?? UIFont.systemFont(ofSize: 16),
            .paragraphStyle: {
                let style = NSMutableParagraphStyle()
                style.alignment = .center
                return style
            }()
        ]
        let attributedPlaceholder = NSAttributedString(string: placeholderText, attributes: attributes)
        inputField.attributedPlaceholder = attributedPlaceholder
        
        // 刷新当前显示的视图控制器
        if let selectedVC = selectedViewController as? UINavigationController {
            selectedVC.viewControllers.forEach { viewController in
                // 触发视图控制器的视图刷新
                viewController.view.setNeedsLayout()
                viewController.view.layoutIfNeeded()
            }
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 每次视图显示时更新本地化文本
        updateLocalizedTexts()
    }
    
    private func updateLocalizedTexts() {
        // 更新输入框占位文本
        let placeholderText = "输入你的意图".localized()
        print("🔄 更新本地化文本 - 当前语言: \(Localize.currentLanguage()), 本地化结果: '\(placeholderText)'")
        
        let attributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor.lightGray,
            .font: UIFont(name: "Avenir", size: 15) ?? UIFont.systemFont(ofSize: 16),
            .paragraphStyle: {
                let style = NSMutableParagraphStyle()
                style.alignment = .center
                return style
            }()
        ]
        let attributedPlaceholder = NSAttributedString(string: placeholderText, attributes: attributes)
        inputField.attributedPlaceholder = attributedPlaceholder
        
        // 更新TabBar标题
        if let items = tabBar.items {
            items[0].title = "消息".localized()
            items[1].title = "Chill时刻".localized()
            items[2].title = "朋友".localized()
            items[3].title = "我的".localized()
        }
    }

    // 添加公共方法用于显示登录页面
    static func showLoginController() {
        print("DEBUG: MainTabViewController.showLoginController called")
        DispatchQueue.main.async {
            guard let shared = MainTabViewController.shared else {
                print("DEBUG: MainTabViewController.shared is nil")
                return
            }
            shared.presentLoginController()
        }
    }
}

extension MainTabViewController: ContactsDataSource {
    func getFrequentUsers() -> [OIMUserInfo] {
        guard let uid = AccountViewModel.userID else { return [] }
        guard let usersJson = UserDefaults.standard.object(forKey: uid) as? String else { return [] }
        
        guard let users = JsonTool.fromJson(usersJson, toClass: [UserEntity].self) else {
            return []
        }
        let current = Int(Date().timeIntervalSince1970)
        let oUsers: [OIMUserInfo] = users.compactMap { (user: UserEntity) in
            if current - user.savedTime <= 7 * 24 * 3600 {
                return user.toOIMUserInfo()
            }
            return nil
        }
        return oUsers
    }
    
    func setFrequentUsers(_ users: [OIMUserInfo]) {
        guard let uid = AccountViewModel.userID else { return }
        let saveTime = Int(Date().timeIntervalSince1970)
        let before = getFrequentUsers()
        var mUsers: [OIMUserInfo] = before
        mUsers.append(contentsOf: users)
        let ret = mUsers.deduplicate(filter: {$0.userID})
        
        let uEntities: [UserEntity] = ret.compactMap { (user: OIMUserInfo) in
            var uEntity = UserEntity.init(user: user)
            uEntity.savedTime = saveTime
            return uEntity
        }
        let json = JsonTool.toJson(fromObject: uEntities)
        UserDefaults.standard.setValue(json, forKey: uid)
        UserDefaults.standard.synchronize()
    }
    
    struct UserEntity: Codable {
        var userID: String?
        var nickname: String?
        var faceURL: String?
        var savedTime: Int = 0
        
        init(user: OIMUserInfo) {
            self.userID = user.userID
            nickname = user.nickname
            faceURL = user.faceURL
        }
        
        func toOIMUserInfo() -> OIMUserInfo {
            let item = OIMUserInfo.init()
            item.userID = userID
            item.nickname = nickname
            item.faceURL = faceURL
            return item
        }
    }
}

extension UIImage {
    func resized(to size: CGSize) -> UIImage {
        return UIGraphicsImageRenderer(size: size).image { _ in
            draw(in: CGRect(origin: .zero, size: size))
        }
    }
}

// MARK: - Launch Screen Delay
extension MainTabViewController {

    /// 显示启动屏延迟效果
    private func showLaunchScreenDelay() {
        // 延迟检查登录状态，等待SDK完全初始化
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.checkLoginStatusImmediately()
        }

        // 4秒后执行启动屏结束后的操作
        DispatchQueue.main.asyncAfter(deadline: .now() + 4.0) { [weak self] in
            // 启动屏结束后，显示输入框（如果当前在消息页面）
            self?.showInputContainerIfNeeded()
        }
    }



    /// 启动屏结束后显示输入框（如果需要）
    private func showInputContainerIfNeeded() {
        // 只有在消息页面才显示输入框，立即显示，不需要等待网络连接
        if selectedIndex == messageTabIndex {
            inputContainerView.isHidden = false
            inputContainerView.alpha = 1  // 立即设置为可见
            updateInputContainerPosition()
        }
    }

    /// 启动屏结束后初始化所有延迟操作
    private func initializeAfterLaunchScreen() {
        // 不再重复检查登录状态，避免多次登录尝试
        print("DEBUG: 启动屏结束，初始化完成")
    }

    /// 立即检查登录状态
    private func checkLoginStatusImmediately() {
        // 防止重复检查
        if hasCheckedLoginStatus {
            print("DEBUG: 登录状态已检查过，跳过重复检查")
            return
        }
        hasCheckedLoginStatus = true

        // 检查是否有保存的登录凭据
        if let uid = UserDefaults.standard.object(forKey: AccountViewModel.IMUidKey) as? String,
           let token = UserDefaults.standard.object(forKey: AccountViewModel.IMTokenKey) as? String,
           let chatToken = UserDefaults.standard.object(forKey: AccountViewModel.bussinessTokenKey) as? String {
            print("DEBUG: Found saved credentials - userID: \(uid)")
            // 静默登录，不显示 ProgressHUD
            AccountViewModel.loginIM(uid: uid, imToken: token, chatToken: chatToken) {[weak self] (errCode, errMsg) in
                if errMsg != nil {
                    print("DEBUG: Login failed silently: \(errMsg ?? "Unknown error")")
                    // 登录失败时显示登录页面
                    DispatchQueue.main.async {
                        self?.presentLoginController()
                    }
                } else {
                    print("DEBUG: Login successful")
                    self?.pushBindAlias()
                }
            }
        } else {
            print("DEBUG: No saved credentials found")
            // 立即显示登录页面，不让用户看到对话列表
            DispatchQueue.main.async {
                self.presentLoginController()
            }
        }
    }
}
