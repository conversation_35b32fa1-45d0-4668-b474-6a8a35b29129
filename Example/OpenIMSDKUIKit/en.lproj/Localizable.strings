"通讯录" = "Contacts";
"我的" = "Me";
"发现" = "Discovery";
"朋友圈" = "Moments";
"个人信息" = "Profile";
"我的意图记忆" = "My Intent Memory";
"我的分享" = "My Shares";
"语言设置" = "Language Settings";
"退出登录" = "Log Out";
"关于我们" = "About Us";
"版本号" = "Version";
"跟随系统" = "Follow System";
"简体中文" = "Simplified Chinese";
"English" = "English";
"请稍候" = "Please wait";
"10001" = "Invalid request parameters";
"10002" = "Database error";
"10003" = "Server error";
"10006" = "Record not found";
"20001" = "Account already registered";
"20002" = "Verification code sent repeatedly";
"20003" = "Invalid invitation code";
"20004" = "Registration IP restricted";
"30001" = "Invalid verification code";
"30002" = "Verification code expired";
"30003" = "Invitation code used";
"30004" = "Invitation code not found";
"40001" = "Account not registered";
"40002" = "Wrong password";
"40003" = "Login IP restricted";
"40004" = "IP blocked for registration and login";
"50001" = "Expired";
"50002" = "Format error";
"50003" = "Not effective";
"50004" = "Unknown error";
"50005" = "Creation error";

// Intent List
"intentListAll" = "All Intents";
"intentListLongTerm" = "Long Intents";
"intentListShortTerm" = "Short Intents";
"intentListValidUntil" = "Valid until: %@";
"intentListPause" = "Pause";
"intentListRun" = "Run";
"intentListDelete" = "Delete";
"intentListStatusRunning" = "Running";
"intentListStatusPaused" = "Paused";
"intentListOperationSuccess" = "Operation successful";
"intentListOperationFailed" = "Operation failed: %@";
"intentListDeleteSuccess" = "Deleted successfully";
"intentListDeleteFailed" = "Delete failed";

// UserDetailTableViewController
"personalHomepage" = "Profile";
"chat" = "Chat";
"addFriend" = "Add Friend";
"subscribe" = "Follow";
"subscribed" = "Following";
"myUpdates" = "My Posts";
"idCopied" = "ID Copied";
"alreadyFriends" = "Already Friends";
"friendRequestSent" = "Friend Request Sent";
"sendRequestFailed" = "Request Failed";
"unknownError" = "Unknown Error";
"subscribeSuccess" = "Followed Successfully";
"subscribeFailed" = "Follow Failed";
"unsubscribeSuccess" = "Unfollowed Successfully";
"unsubscribeFailed" = "Unfollow Failed";
"groupNickname" = "Group Nickname";
"joinTime" = "Join Time";
"joinMethod" = "Join Method";
"profile" = "Profile";

"从相册中选取" = "Choose from Album";
"照相" = "Take Photo";
"取消" = "Cancel";

// Intent Feed
"Chill时刻" = "Chill Moment";
"订阅动态" = "Subscription Feed";
"暂无意图动态" = "No Intent Feed";
"暂无订阅动态" = "No Subscription Feed";
"网络连接问题，请检查网络后重试" = "Network connection issue, please check your network and try again";
"未收到数据" = "No data received";
"服务器返回错误: %d" = "Server error: %d";
"加载失败" = "Loading failed";
"加载失败: %@" = "Loading failed: %@";
"数据解析错误" = "Data parsing error";
"数据解析失败，请稍后重试" = "Data parsing failed, please try again later";
"数据结构已改变，请更新应用" = "Data structure has changed, please update the app";
"无法解析返回数据" = "Cannot parse returned data";
"未知来源" = "Unknown source";
"加载失败，点击重试" = "Loading failed, tap to retry";

// Intent Details
"意图：" = "Intent: ";
"匹配度：" = "Match rate: ";
"匹配原因：" = "Match reason: ";

// Actions
"评论" = "Comment";
"转发" = "Forward";
"点赞" = "Like";
"收藏" = "Favorite";

// FeedDetailViewController strings
"评论" = "Comments";
"说点什么..." = "Say something...";
"评论成功" = "Comment posted successfully";
"发布评论失败" = "Failed to post comment";
"刚刚" = "Just now";
"分钟前" = "mins ago";
"小时前" = "hrs ago";
"昨天" = "Yesterday";
"天前" = "days ago";
"网络连接问题，请稍后重试" = "Network connection issue, please try again later";
"无效的服务器响应" = "Invalid server response";
"服务器未返回数据" = "No data returned from server";
"数据解析失败" = "Data parsing failed";
"未知错误" = "Unknown error";
"无法获取用户信息" = "Cannot retrieve user information";
"点赞" = "Like";
"收藏" = "Favorite";
"转发" = "Share";
"加载失败，点击重试" = "Loading failed, tap to retry";

// PublishIntentViewController
"intent_immediate" = "Execute immediately";
"intent_one_hour" = "Valid for 1 hour";
"intent_one_day" = "Valid for 24 hours";
"intent_one_week" = "Valid for one week";
"intent_one_month" = "Valid for one month";
"intent_long_term" = "Valid long term";
"intent_time_selection" = "Select intent execution time";
"intent_placeholder" = "Post your intent 

to help you get information and complete tasks";
"intent_public" = "Public intent";
"intent_private" = "Private intent";
"intent_cancel" = "Cancel";
"intent_publish" = "Post Intent";
"intent_enter_content" = "Please enter content";
"intent_message_tag" = "Intent Message";

// Permissions
"camera_permission_denied" = "Please allow camera access in Settings";
"photo_permission_denied" = "Please allow photo library access in Settings";
"location_permission_denied" = "Please allow location access in Settings";
"location_fetch_failed" = "Failed to get location information";

// Default values
"default_username" = "Chillman";

// MainTabViewController
"输入你的意图" = "Enter your intent";
"消息" = "Messages";
"朋友" = "Friends";

// Login Page
"Stay chill" = "Stay chill";
"手机号" = "Phone Number";
"注册账号" = "Register";
"忘记密码" = "Forgot Password";
"验证码登录" = "Code Login";
"密码登录" = "Password Login";
"请输入密码" = "Enter Password";
"登录" = "Login";
"我已阅读并同意" = "I have read and agree to";
"《服务协议》《隐私权政策》" = "Terms of Service and Privacy Policy";
"还没有账号？" = "Don't have an account?";
"邮箱登录" = "Email Login";
"验证码发送成功" = "Verification code sent successfully";
"请输入手机号码" = "Enter phone number";
"请输入正确的手机号" = "Please enter a valid phone number";
"请输入密码或验证码" = "Please enter password or verification code";
"密码" = "Password";

// Country Selection
"选择国家/地区" = "Select Country/Region";
"取消" = "Cancel";
"搜索" = "Search";
"搜索国家或区号" = "Search Country/Region";
"错误" = "Error";
"无法访问国家/地区代码" = "Cannot access country/region codes";
"JSON错误" = "JSON Error";
"无法解析国家/地区数据" = "Cannot parse country/region data";
"巴勒斯坦" = "Palestine";
"确定" = "Confirm";

// Registration Page
"注册" = "Register";
"女" = "Female";
"男" = "Male";
"未知" = "Unknown";
"点击选择头像" = "Tap to select avatar";
"已选择头像" = "Avatar selected";
"请输入手机号" = "Enter phone number";
"请输入密码" = "Enter password";
"请确认密码" = "Confirm password";
"请输入昵称" = "Enter nickname";
"性别" = "Gender";
"生日" = "Birthday";
"年" = "Year";
"月" = "Month";
"日" = "Day";
"手机号格式不正确" = "Invalid phone number format";
"密码不能少于6位" = "Password must be at least 6 characters";
"两次输入的密码不一致" = "Passwords do not match";
"注册中..." = "Registering...";
"注册成功！" = "Registration successful!";
"请使用手机号和密码登录" = "Please login with your phone number and password";
"注册失败: 请先获取验证码" = "Registration failed: Please get verification code first";
"注册失败" = "Registration failed";
"错误码" = "Error code";

"登录中..." = "Logging in...";
"登录成功" = "Login successful";
"登录失败" = "Login failed";
"发送失败" = "Send failed";
"验证码发送成功" = "Verification code sent successfully";

"PasswordError" = "Password error";
"VerificationCodeError" = "Verification code error";
"AccountNotExist" = "Account does not exist";
"NetworkError" = "Network error";
"ServerError" = "Server error";
"UnknownError" = "Unknown error";

"意图动态" = "Intent Feed";

// 下拉刷新结果提示
"已更新 %d 条新内容，并同步了最新状态" = "Updated %d new posts and synced latest status";
"已加载 %d 条新内容" = "Loaded %d new posts";
"已同步最新状态，暂无新内容" = "Synced latest status, no new content";
"已是最新内容" = "Already up to date";
