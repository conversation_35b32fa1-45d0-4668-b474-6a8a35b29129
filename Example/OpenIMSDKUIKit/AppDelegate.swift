import OUICore
import Localize_Swift
import RxSwift
import ProgressHUD
//import GTSDK

let kGtAppId = ""
let kGtAppKey = ""
let kGtAppSecret = ""

//The domain name used by default
let defaultHost = GlobalConfig.defaultHost

// The default IP or domain name used in the settings page. After the settings page is saved, defaultHost will become invalid.
let defaultIP = GlobalConfig.defaultIP
let defaultDomain = GlobalConfig.defaultDomain

// 全局API基础URL配置
let apiBaseURL = GlobalConfig.apiBaseURL

let businessPort = GlobalConfig.businessPort
let businessRoute = GlobalConfig.businessRoute

let adminPort = GlobalConfig.adminPort
let adminRoute = GlobalConfig.adminRoute
let sdkAPIPort = GlobalConfig.sdkAPIPort
let sdkAPIRoute = GlobalConfig.sdkAPIRoute
let sdkWSPort = GlobalConfig.sdkWSPort
let sdkWSRoute = GlobalConfig.sdkWSRoute

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate, UNUserNotificationCenterDelegate {
    
    var backgroundTaskIdentifier: UIBackgroundTaskIdentifier?
    
    var window: UIWindow?
    private let _disposeBag = DisposeBag();
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        // 初始化语言设置
        initializeLanguageSettings()
        
        // 配置本地化管理器
        LocalizationManager.configure()
        
        // 测试本地化
        testLocalization()
        
        UINavigationBar.appearance().tintColor = .c0C1C33
        // Main configuration here, pay attention to the differences between http and https, ws and wss, IP uses port, domain name uses routing
        let enableTLS = UserDefaults.standard.object(forKey: useTLSKey) == nil
        ? false : UserDefaults.standard.bool(forKey: useTLSKey)
        
        let httpScheme = enableTLS ? "https://" : "http://"
        let wsScheme = enableTLS  ? "wss://" : "ws://"
        
//        let predicateStr = "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
//        let predicate = NSPredicate(format: "SELF MATCHES %@", predicateStr)
//        let isIP = predicate.evaluate(with: defaultHost)
        
        let enableDomain = UserDefaults.standard.object(forKey: useDomainKey) == nil
        ? false : UserDefaults.standard.bool(forKey: useDomainKey)
        
        let serverAddress = UserDefaults.standard.string(forKey: serverAddressKey) ?? defaultHost
        
        // Set and retrieve global configuration
        UserDefaults.standard.setValue(httpScheme + serverAddress + (!enableDomain ? adminPort : adminRoute), forKey: adminServerAddrKey)

        // Set login, registration, and more - AccountViewModel
        UserDefaults.standard.setValue(httpScheme + serverAddress + (!enableDomain ? businessPort: businessRoute), forKey: bussinessServerAddrKey)

        // Set SDK API address
        let sdkAPIAddress = UserDefaults.standard.string(forKey: sdkAPIAddrKey) ??
        httpScheme + serverAddress + (!enableDomain ? sdkAPIPort : sdkAPIRoute)

        // Set WebSocket address
        let sdkWebSocketAddress = UserDefaults.standard.string(forKey: sdkWSAddrKey) ??
        wsScheme + serverAddress + (!enableDomain ? sdkWSPort : sdkWSRoute)

        // Set object storage
        let sdkObjectStorage = UserDefaults.standard.string(forKey: sdkObjectStorageKey) ??
        "minio"

        // Initialize the SDK
        IMController.shared.setup(sdkAPIAdrr: sdkAPIAddress,
                                  sdkWSAddr: sdkWebSocketAddress) {
            IMController.shared.currentUserRelay.accept(nil)
            AccountViewModel.saveUser(uid: nil, imToken: nil, chatToken: nil)
            NotificationCenter.default.post(name: .init("logout"), object: nil)
        }
        
//        GeTuiSdk.start(withAppId: kGtAppId, appKey: kGtAppKey, appSecret: kGtAppSecret, delegate: self)
//        GeTuiSdk.registerRemoteNotification([.alert, .badge, .sound])
    
        AccountViewModel.getClientConfig()

        // 配置统一的ProgressHUD样式
        configureProgressHUD()

        // 添加语言变更通知监听
        NotificationCenter.default.addObserver(self,
                                             selector: #selector(handleLanguageChange),
                                             name: NSNotification.Name("AppLanguageChanged"),
                                             object: nil)
        
        // 同时监听系统自带的语言变更通知
        NotificationCenter.default.addObserver(self, 
                                             selector: #selector(handleLanguageChange), 
                                             name: NSNotification.Name(LCLLanguageChangeNotification), 
                                             object: nil)
        
        return true
    }
    
    // 配置统一的ProgressHUD样式
    private func configureProgressHUD() {
        // 设置统一的颜色主题
        ProgressHUD.colorHUD = UIColor.systemBackground
        ProgressHUD.colorStatus = UIColor.label
        ProgressHUD.colorAnimation = UIColor.black // 大C使用黑色
        ProgressHUD.colorBackground = UIColor(red: 0, green: 0, blue: 0, alpha: 0.3)

        // 设置统一的字体
        ProgressHUD.fontStatus = UIFont.f17

        // 设置成功和错误图标
        if let successImage = UIImage(systemName: "checkmark.circle.fill") {
            ProgressHUD.imageSuccess = successImage.withTintColor(StandardUI.color_10CC64, renderingMode: .alwaysOriginal)
        }
        if let errorImage = UIImage(systemName: "xmark.circle.fill") {
            ProgressHUD.imageError = errorImage.withTintColor(StandardUI.color_F44038, renderingMode: .alwaysOriginal)
        }

        // 设置大小
        ProgressHUD.mediaSize = 80
        ProgressHUD.marginSize = 40

        // 设置动画颜色为黑色，用于大C显示
        ProgressHUD.colorAnimation = UIColor.black
    }

    @objc func handleLanguageChange() {
        // 使用通知中心发送语言变更通知，让各个视图控制器自行更新
        NotificationCenter.default.post(name: NSNotification.Name("RefreshUIForLanguageChange"), object: nil)
        
        // 延迟一小段时间后刷新UI，避免立即刷新导致的问题
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            // 刷新根视图控制器以应用新的语言
            if let rootViewController = self?.window?.rootViewController {
                // 如果是UITabBarController，刷新所有标签页的标题
                if let tabBarController = rootViewController as? UITabBarController {
                    // 使用LocalizationManager更新TabBar的标题
                    LocalizationManager.shared.refreshTabBarTitles()
                    
                    for (index, viewController) in tabBarController.viewControllers?.enumerated() ?? [].enumerated() {
                        if let navController = viewController as? UINavigationController {
                            // 只更新导航控制器的标题，不重新加载视图
                            if let topVC = navController.topViewController {
                                topVC.navigationItem.title = topVC.title?.localized()
                            }
                        }
                    }
                }
                
                // 使用轻量级的方式刷新UI，不重新加载整个视图层次结构
                UIView.animate(withDuration: 0.3) {
                    rootViewController.view.alpha = 0.99
                } completion: { _ in
                    UIView.animate(withDuration: 0.3) {
                        rootViewController.view.alpha = 1.0
                    }
                }
            }
        }
    }
    
    func applicationWillResignActive(_ application: UIApplication) {
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        application.applicationIconBadgeNumber = 0
        self.backgroundTaskIdentifier = UIApplication.shared.beginBackgroundTask(withName: "taskname", expirationHandler: {
            
            if (self.backgroundTaskIdentifier != .invalid) {
                UIApplication.shared.endBackgroundTask(self.backgroundTaskIdentifier!);
                self.backgroundTaskIdentifier = .invalid;
            }
        });
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        application.applicationIconBadgeNumber = 0
        UIApplication.shared.endBackgroundTask(self.backgroundTaskIdentifier!);
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        
    }
    
    func applicationWillTerminate(_ application: UIApplication) {
        
    }
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    }
    
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("did Fail To Register For Remote Notifications With Error: %@", error)
    }
    
    // MARK: - GeTuiSdkDelegate
    func geTuiSdkDidRegisterClient(_ clientId: String) {
        let msg = "[ TestDemo ] \(#function):\(clientId)"
        print(msg)
    }
    
    func geTuiSdkDidOccurError(_ error: Error) {
        let msg = "[ TestDemo ] \(#function) \(error.localizedDescription)"
        print(msg)
    }
    
    func getuiSdkGrantAuthorization(_ granted: Bool, error: Error?) {
        let msg = "[ TestDemo ] \(#function) \(granted ? "Granted":"NO Granted")"
        print(msg)
    }
    
    @available(iOS 10.0, *)
    func geTuiSdkNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        completionHandler([.badge, .sound, .alert])
    }
    
    @available(iOS 10.0, *)
    func geTuiSdkDidReceiveNotification(_ userInfo: [AnyHashable : Any], notificationCenter center: UNUserNotificationCenter?, response: UNNotificationResponse?, fetchCompletionHandler completionHandler: ((UIBackgroundFetchResult) -> Void)? = nil) {
        completionHandler?(.noData)
    }
    
    func pushLocalNotification(_ title: String, _ userInfo:[AnyHashable:Any]) {
        if #available(iOS 10.0, *) {
            let content = UNMutableNotificationContent()
            content.title = title
            content.body = title
            let req = UNNotificationRequest.init(identifier: "id1", content: content, trigger: nil)
            
            UNUserNotificationCenter.current().add(req) { _ in
                print("addNotificationRequest added")
            }
        }
    }
    
    func geTuiSdkDidReceiveSlience(_ userInfo: [AnyHashable : Any], fromGetui: Bool, offLine: Bool, appId: String?, taskId: String?, msgId: String?, fetchCompletionHandler completionHandler: ((UIBackgroundFetchResult) -> Void)? = nil) {
        var dic: [AnyHashable : Any] = [:]
        if fromGetui {
            
            dic = ["_gmid_":"\(String(describing: taskId)):\(String(describing: msgId))"]
        } else {
            //APNs静默通知
            dic = userInfo;
        }
        if fromGetui && !offLine {
            pushLocalNotification(userInfo["payload"] as! String, dic)
        }
    }
    
    @available(iOS 10.0, *)
    func geTuiSdkNotificationCenter(_ center: UNUserNotificationCenter, openSettingsFor notification: UNNotification?) {
    }
    
    func geTuiSdkDidSendMessage(_ messageId: String, result: Int32) {
        let msg = "[ TestDemo ] \(#function) \(String(describing: messageId)), result=\(result)"
        print(msg)
    }
    
    func geTuiSdkDidAliasAction(_ action: String, result isSuccess: Bool, sequenceNum aSn: String, error aError: Error?) {
    }
    
    
    //MARK: - 标签设置
    func geTuiSdkDidSetTagsAction(_ sequenceNum: String, result isSuccess: Bool, error aError: Error?) {
        
        let msg = "[ TestDemo ] \(#function)  sequenceNum:\(sequenceNum) isSuccess:\(isSuccess) error: \(String(describing: aError))"
        
        print(msg)
    }
    
    // 初始化语言设置
    private func initializeLanguageSettings() {
        // 检查是否有用户明确设置的语言选项
        if let savedLanguage = UserDefaults.standard.string(forKey: "LCLCurrentLanguageKey") {
            if savedLanguage == "system" {
                // 如果设置为跟随系统，使用系统语言
                Localize.resetCurrentLanguageToDefault()
            } else {
                // 如果有保存的具体语言设置，使用保存的设置
                Localize.setCurrentLanguage(savedLanguage)
            }
        } else {
            // 如果没有任何设置，默认跟随系统
            UserDefaults.standard.set("system", forKey: "LCLCurrentLanguageKey")
            UserDefaults.standard.synchronize()
            Localize.resetCurrentLanguageToDefault()
        }
        
        // 发送语言变更通知，确保所有UI都更新
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: NSNotification.Name("AppLanguageChanged"), object: nil)
        }
    }
    
    // 测试本地化功能
    private func testLocalization() {
        print("\n===== 应用启动时本地化测试 =====")
        
        // 检查当前语言
        print("🌐 Current language:", Localize.currentLanguage())
        
        // 测试不同的本地化字符串
        let keys = ["personalHomepage", "myUpdates", "chat", "addFriend", "subscribe"]
        
        for key in keys {
            print("\n🔑 Testing key:", key)
            
            // 直接使用字符串
            let directResult = key.innerLocalized()
            print("📝 Direct result:", directResult)
            
            // 使用中文字符串
            let chineseKey: String
            switch key {
            case "personalHomepage": chineseKey = "个人主页"
            case "myUpdates": chineseKey = "我的动态"
            case "chat": chineseKey = "聊天"
            case "addFriend": chineseKey = "加好友"
            case "subscribe": chineseKey = "订阅"
            default: chineseKey = key
            }
            
            let chineseResult = chineseKey.innerLocalized()
            print("🇨🇳 Chinese key result:", chineseResult)
        }
        
        print("\n===== 本地化测试结束 =====")
    }
}

