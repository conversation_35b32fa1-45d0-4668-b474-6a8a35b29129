import OUICore
import RxRelay

class FriendListViewModel {
    let lettersRelay: BehaviorRelay<[String]> = .init(value: [])
    var myFriends: [UserInfo] = [] {
        didSet {
            debugFriendListChange(oldValue: oldValue, newValue: myFriends)
        }
    }
    var contactSections: [[UserInfo]] = []

    var myGroups: [GroupInfo] = []
    var groupsSections: [[GroupInfo]] = []
    
    func getMyFriendList() {
        print("\n🔍 [FriendListViewModel] 开始获取好友列表")
        print("📊 当前状态: 用户ID=\(IMController.shared.uid), 连接=\(IMController.shared.connectionRelay.value.title), 内存好友数=\(myFriends.count)")

        let startTime = Date()

        IMController.shared.getFriendList(onSuccess: { [weak self] users in
            let timeElapsed = Date().timeIntervalSince(startTime)

            print("\n📥 [FriendListViewModel] API返回: 耗时=\(String(format: "%.2f", timeElapsed))s, 原始数量=\(users.count)")

            // 数据转换监控
            let r = users.compactMap({ UserInfo(userID: $0.userID!, nickname: $0.showName, faceURL: $0.faceURL) })

            print("🔄 [FriendListViewModel] 数据转换: 转换后数量=\(r.count)")
            if users.count != r.count {
                print("⚠️ [FriendListViewModel] 数据转换丢失: \(users.count - r.count) 个好友")
            }

            // 程序逻辑检查
            if users.isEmpty {
                print("❌ [FriendListViewModel] 程序逻辑问题: API返回空数据")
                print("   - 当前登录状态: \(IMController.shared.getLoginUserID())")
                print("   - 连接状态: \(IMController.shared.connectionRelay.value.title)")
                print("   - 内存缓存: \(self?.myFriends.count ?? 0) 个好友")
            }

            // 更新数据（触发didSet监控）
            self?.myFriends = r
            self?.divideUsersInSection(users: r)
        })
    }
    
    func getGroups() {
        IMController.shared.getJoinedGroupList { [weak self] groups in
            self?.myGroups = groups
            self?.divideGroupsInSection(groups)
        }
    }

    func getUsersAt(indexPaths: [IndexPath]) -> [UserInfo] {
        var users: [UserInfo] = []
        for indexPath in indexPaths {
            let user = contactSections[indexPath.section][indexPath.row]
            users.append(user)
        }
        return users
    }

    func createConversationWith(users: [UserInfo], onSuccess: @escaping CallBack.VoidReturnVoid) {
        IMController.shared.createGroupConversation(users: users) { (_: GroupInfo?) in
            onSuccess()
        }
    }

    private func divideUsersInSection(users: [UserInfo]) {
        DispatchQueue.global().async { [weak self] in
            // 清空旧数据，避免数据重复或冲突
            self?.contactSections.removeAll()
            
            // 记录没有昵称的用户
            var noNameUsers: [UserInfo] = []
            
            var letterSet: Set<String> = []
            for user in users {
                if let firstLetter = user.nickname?.getFirstPinyinUppercaseCharactor() {
                    letterSet.insert(firstLetter)
                } else {
                    // 记录没有有效昵称的用户
                    noNameUsers.append(user)
                }
            }

            // 如果有空昵称用户，添加特殊分组"#"
            if !noNameUsers.isEmpty {
                letterSet.insert("#")
            }

            let letterArr: [String] = Array(letterSet)
            let ret = letterArr.sorted { $0 < $1 }
            
            print("\n=== 好友分组信息 ===")
            print("总分组数量: \(ret.count)")
            print("分组索引: \(ret)")
            print("====================\n")

            for letter in ret {
                var sectionArr: [UserInfo] = []
                // 处理特殊分组"#"
                if letter == "#" {
                    sectionArr.append(contentsOf: noNameUsers)
                } else {
                    // 正常分组
                    for user in users {
                        if let first = user.nickname?.getFirstPinyinUppercaseCharactor(), first == letter {
                            sectionArr.append(user)
                        }
                    }
                }
                self?.contactSections.append(sectionArr)
                print("分组[\(letter)]包含 \(sectionArr.count) 个好友")
            }
            
            DispatchQueue.main.async {
                self?.lettersRelay.accept(ret)
                
                // 在主线程上打印最终分组情况
                print("\n=== 好友列表处理完成 ===")
                print("总分组数: \(self?.lettersRelay.value.count ?? 0)")
                print("总好友数: \(self?.contactSections.flatMap { $0 }.count ?? 0)")
                print("========================\n")
            }
        }
    }
    
    private func divideGroupsInSection(_ users: [GroupInfo]) {
        DispatchQueue.global().async { [weak self] in
            var letterSet: Set<String> = []
            for user in users {
                if let firstLetter = user.groupName?.getFirstPinyinUppercaseCharactor() {
                    letterSet.insert(firstLetter)
                }
            }

            let letterArr: [String] = Array(letterSet)
            let ret = letterArr.sorted { $0 < $1 }

            for letter in ret {
                var sectionArr: [GroupInfo] = []
                for user in users {
                    if let first = user.groupName?.getFirstPinyinUppercaseCharactor(), first == letter {
                        sectionArr.append(user)
                    }
                }
                self?.groupsSections.append(sectionArr)
            }
            DispatchQueue.main.async {
                self?.lettersRelay.accept(ret)
            }
        }
    }

    // MARK: - 调试方法

    /// 监控好友列表变化的简单调试方法
    private func debugFriendListChange(oldValue: [UserInfo], newValue: [UserInfo]) {
        print("\n🔄 [FriendListViewModel] 好友列表数据变化:")
        print("   - 变化前: \(oldValue.count) 个好友")
        print("   - 变化后: \(newValue.count) 个好友")
        print("   - 变化量: \(newValue.count - oldValue.count)")

        if oldValue.count != newValue.count {
            if newValue.count > oldValue.count {
                print("   ✅ 增加了 \(newValue.count - oldValue.count) 个好友")
            } else {
                print("   ❌ 减少了 \(oldValue.count - newValue.count) 个好友")
            }
        }

        // 检查数据质量
        let invalidCount = newValue.filter { $0.userID?.isEmpty != false }.count
        if invalidCount > 0 {
            print("   ⚠️ 发现 \(invalidCount) 个无效好友数据")
        }
    }

    /// 验证好友数据质量
    private func validateFriendData(_ friends: [FullUserInfo]) {
        var issues: [String] = []

        for (index, friend) in friends.enumerated() {
            if friend.userID?.isEmpty != false {
                issues.append("好友\(index): userID为空")
            }
            if friend.showName?.isEmpty != false {
                issues.append("好友\(index): showName为空")
            }
        }

        if !issues.isEmpty {
            print("⚠️ [FriendListViewModel] 数据质量问题:")
            for issue in issues.prefix(5) {
                print("   - \(issue)")
            }
            if issues.count > 5 {
                print("   - ...还有 \(issues.count - 5) 个问题")
            }
        }
    }
}
