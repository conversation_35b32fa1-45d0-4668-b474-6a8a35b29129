import RxSwift
import OUICore
import OUICoreView
import Localize_Swift

// 将 extension 移到文件作用域
extension FriendListViewController: UISearchBarDelegate {
    public func searchBarTextDidBeginEditing(_ searchBar: UISearchBar) {
        // 将"Cancel"本地化逻辑封装到一个单独的方法中
        let cancelText = localizedCancelButtonText()
        
        // 打印调试信息
        print("\n=== 搜索栏取消按钮本地化 ===")
        print("当前语言: \(Localize.currentLanguage())")
        print("本地化结果: \(cancelText)")
        print("===========================\n")
        
        // 设置取消按钮文本
        searchBar.setValue(cancelText, forKey: "cancelButtonText")
        
        // 延迟执行，确保UI已完全加载
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 尝试直接访问取消按钮
            if let cancelButton = searchBar.value(forKey: "cancelButton") as? UIButton {
                cancelButton.setTitle(cancelText, for: .normal)
                cancelButton.setTitle(cancelText, for: .highlighted)
            }
        }
    }
    
    /// 获取本地化的取消按钮文本，支持多语言
    private func localizedCancelButtonText() -> String {
        // 1. 尝试使用标准的本地化机制
        let localizedText = NSLocalizedString("Cancel", comment: "Search bar cancel button")
        
        // 2. 检查本地化是否成功 - 如果返回值与原始字符串相同，说明没有找到本地化文本
        if localizedText != "Cancel" {
            // 本地化成功，直接返回
            return localizedText
        }
        
        // 3. 本地化失败，使用内置的语言映射作为回退方案
        let currentLang = Localize.currentLanguage().lowercased()
        
        // 支持更多语言的映射表
        let cancelTextMap: [String: String] = [
            "en": "Cancel",
            "zh-hans": "取消",
            "zh-hant": "取消",
            "zh": "取消",
            "fr": "Annuler",
            "es": "Cancelar",
            "de": "Abbrechen",
            "ja": "キャンセル",
            "ko": "취소",
            "ru": "Отмена",
            "it": "Annulla",
            "pt": "Cancelar",
            "ar": "إلغاء",
            "th": "ยกเลิก"
            // 可以根据需要继续添加更多语言
        ]
        
        // 4. 查找当前语言的映射
        for (langPrefix, text) in cancelTextMap {
            if currentLang.hasPrefix(langPrefix) {
                return text
            }
        }
        
        // 5. 如果所有尝试都失败，则根据是否为英文环境返回对应文本
        if currentLang.hasPrefix("en") || Locale.current.languageCode == "en" {
            return "Cancel"
        } else {
            // 默认返回中文，作为最后的回退
            return "取消"
        }
    }
    
    public func searchBarCancelButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
    }
}

open class FriendListViewController: UIViewController {
    public var selectCallBack: ((UserInfo) -> Void)?
    public var showsBackButton: Bool = true
    public var isFromTabBar: Bool = false
    private lazy var _tableView: UITableView = {
        let v = UITableView()
        let config = SCIndexViewConfiguration(indexViewStyle: SCIndexViewStyle.default)!
        config.indexItemRightMargin = 8
        config.indexItemTextColor = UIColor(hexString: "#555555")
        config.indexItemSelectedBackgroundColor = UIColor(hexString: "#57be6a")
        config.indexItemsSpace = 4
        v.sc_indexViewConfiguration = config
        v.sc_translucentForTableViewInNavigationBar = true
        v.register(FriendListUserTableViewCell.self, forCellReuseIdentifier: FriendListUserTableViewCell.className)
        v.register(ContactsEntranceTableViewCell.self, forCellReuseIdentifier: "NewFriendCell")
        v.dataSource = self
        v.delegate = self
        v.rowHeight = UITableView.automaticDimension
        v.backgroundColor = .clear
        
        if #available(iOS 15.0, *) {
            v.sectionHeaderTopPadding = 0
        }
        return v
    }()

    private let _viewModel = FriendListViewModel()
    private let _disposeBag = DisposeBag()
    private lazy var resultC = FriendListResultViewController()

    override open func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationItem.hidesSearchBarWhenScrolling = false

        view.backgroundColor = .white
        _tableView.backgroundColor = .white

        print("📱 [FriendListViewController] viewWillAppear - 当前好友数: \(_viewModel.myFriends.count)")

        // 只有在好友列表为空时才重新加载，避免不必要的重复请求
        if _viewModel.myFriends.isEmpty {
            print("   - 好友列表为空，重新加载")
            _viewModel.getMyFriendList()
        } else {
            print("   - 使用现有数据，无需重新加载")
        }
    }

    override open func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        navigationItem.hidesSearchBarWhenScrolling = true
    }

    override open func viewDidLoad() {
        super.viewDidLoad()

        print("📱 [FriendListViewController] viewDidLoad - 开始初始化")

        navigationItem.title = "MY_FRIENDS".innerLocalized()
        view.backgroundColor = .white

        if isFromTabBar {
            navigationItem.hidesBackButton = true
            navigationItem.leftBarButtonItem = nil
        } else {
            if showsBackButton {
                let backButton = UIBarButtonItem(image: UIImage(nameInBundle: "chat_back_btn_icon"), style: .plain, target: self, action: #selector(backButtonTapped))
                navigationItem.leftBarButtonItem = backButton
            } else {
                navigationItem.hidesBackButton = true
                navigationItem.leftBarButtonItem = nil
            }
        }

        navigationController?.interactivePopGestureRecognizer?.delegate = nil

        initView()
        bindData()

        // 登录时已经预加载了好友数据，这里只在数据为空时加载
        if _viewModel.myFriends.isEmpty {
            print("📱 [FriendListViewController] viewDidLoad - 好友数据为空，开始加载")
            _viewModel.getMyFriendList()
        } else {
            print("📱 [FriendListViewController] viewDidLoad - 好友数据已存在: \(_viewModel.myFriends.count) 个")
        }

        NotificationCenter.default.addObserver(self,
                                               selector: #selector(handleLanguageChange),
                                               name: NSNotification.Name("RefreshUIForLanguageChange"),
                                               object: nil)
    }
    
    /// 处理语言变更通知，更新UI文本
    @objc private func handleLanguageChange() {
        // 更新标题
        navigationItem.title = "MY_FRIENDS".innerLocalized()
        
        // 更新搜索栏
        DispatchQueue.main.async {
            self.configureSearchBar()
            self._tableView.reloadData()
        }
    }

    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }

    private func initView() {
        let searchC: UISearchController = {
            let v = UISearchController(searchResultsController: resultC)
            v.searchResultsUpdater = resultC
            // 直接使用"SEARCH_PLACEHOLDER"键查找对应翻译
            v.searchBar.placeholder = "SEARCH_PLACEHOLDER".innerLocalized()
            v.obscuresBackgroundDuringPresentation = false
            
            if #available(iOS 13.0, *) {
                v.searchBar.searchTextField.backgroundColor = .clear
            }
            
            return v
        }()
        navigationItem.searchController = searchC
        
        // 延迟执行以确保UISearchBar完全加载
        DispatchQueue.main.async {
            self.configureSearchBar()
        }
        
        resultC.selectUserCallBack = { [weak self] uid in
            let vc = UserDetailTableViewController(userId: uid, groupId: nil, userDetailFor: .card)
            self?.navigationController?.pushViewController(vc, animated: true)
        }

        view.addSubview(_tableView)
        _tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func configureSearchBar() {
        guard let searchBar = navigationItem.searchController?.searchBar else { return }
        
        // 添加简化的本地化调试信息
        print("\n=== 配置搜索栏 ===")
        print("当前语言: \(Localize.currentLanguage())")
        
        // 使用相同的本地化方法获取取消按钮文本
        let cancelText = localizedCancelButtonText()
        
        print("设置取消按钮为: \(cancelText)")
        
        // 直接设置取消按钮文本
        searchBar.setValue(cancelText, forKey: "cancelButtonText")
        
        print("==========================================\n")
        
        if #available(iOS 13.0, *) {
            let textField = searchBar.searchTextField
            
            // 增大字体，使用系统字体提升美观度
            textField.font = UIFont.systemFont(ofSize: 14, weight: .regular)
            
            // 设置更美观的淡灰色背景
            textField.backgroundColor = UIColor(white: 0.99, alpha: 0.4)
            
            // 优化圆角
            textField.layer.cornerRadius = 16
            textField.clipsToBounds = true
            
            // 添加细微边框提升质感
            textField.layer.borderWidth = 0.5
            textField.layer.borderColor = UIColor(white: 0.85, alpha: 0.3).cgColor
            
            // 调整搜索框的高度
            textField.frame.size.height = 34
            
            // 获取正确本地化的占位符文本
            let placeholderText = "SEARCH_PLACEHOLDER".innerLocalized()
            print("搜索框占位符设置为：", placeholderText)
            
            // 调整placeholder颜色和大小
            let attributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor.gray.withAlphaComponent(0.7),
                .font: UIFont.systemFont(ofSize: 14, weight: .regular)
            ]
            
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholderText,
                attributes: attributes
            )
        } else {
            // 针对iOS 13以下版本设置占位符
            searchBar.placeholder = "SEARCH_PLACEHOLDER".innerLocalized()
        }
        
        searchBar.delegate = self
        
        // 延迟执行，确保取消按钮完全加载
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 尝试直接访问取消按钮
            if let cancelButton = searchBar.value(forKey: "cancelButton") as? UIButton {
                cancelButton.setTitle(cancelText, for: .normal)
                cancelButton.setTitle(cancelText, for: .highlighted)
            }
        }
    }

    private func bindData() {
        // 先预加载一些临时数据，避免白屏
        preloadData()
        
        _viewModel.lettersRelay.distinctUntilChanged().subscribe(onNext: { [weak self] (values: [String]) in
            guard let sself = self else { return }
            self?.resultC.dataList = sself._viewModel.myFriends
            self?._tableView.sc_indexViewDataSource = values
            self?._tableView.sc_startSection = 0
            self?._tableView.reloadData()
        }).disposed(by: _disposeBag)
    }

    // 添加预加载数据的方法
    private func preloadData() {
        // 创建一些临时数据，在真实数据加载完成前显示
        let tempLetters = ["A", "B", "C"]
        _tableView.sc_indexViewDataSource = tempLetters
        _tableView.sc_startSection = 0
        _tableView.reloadData()
    }

    /// 检查特定用户ID的好友关系状态
    private func checkSpecificFriendStatus() {
        // 这里填入您后台系统中显示的两个好友ID
        let targetUserIDs = ["7431589925", "8371124483"] // ChillMan和CryptoMan的ID
        
        print("\n=== 检查特定好友关系 ===")
        print("正在检查指定用户ID的好友关系...")
        
        // 直接使用SDK的API查询特定用户信息
        IMController.shared.getFriendsInfo(userIDs: targetUserIDs) { users in
            print("查询结果 - 返回用户数量: \(users.count)")
            
            if users.isEmpty {
                print("未找到任何指定用户的好友关系，可能原因:")
                print("1. 这些用户ID在服务器上与当前登录用户没有好友关系")
                print("2. API调用权限不足")
                print("3. 网络连接问题")
            } else {
                print("找到了以下用户的好友关系:")
                for (index, user) in users.enumerated() {
                    // 安全地访问属性
                    let friendStatus = user.friendInfo != nil ? "是好友" : "非好友"
                    let blacklistStatus = user.blackInfo != nil ? "已拉黑" : "未拉黑"
                    let nickname = user.showName ?? "无昵称" // 使用showName，这是FullUserInfo的计算属性
                    
                    print("\(index+1). 用户ID: \(user.userID ?? "未知")")
                    print("   昵称: \(nickname)")
                    print("   好友状态: \(friendStatus)")
                    print("   黑名单状态: \(blacklistStatus)")
                    
                    if let friendInfo = user.friendInfo {
                        // 安全地访问FriendInfo的属性
                        print("   好友备注: \(friendInfo.remark ?? "无")")
                        if friendInfo.createTime > 0 {
                            let date = Date(timeIntervalSince1970: TimeInterval(friendInfo.createTime))
                            let formatter = DateFormatter()
                            formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                            print("   好友关系创建时间: \(formatter.string(from: date))")
                        }
                    }
                }
            }
            
            print("======================================\n")
            
            // 如果服务器上确实没有好友关系，尝试查询好友申请状态
            if users.isEmpty {
                self.checkFriendApplicationStatus()
            }
        }
    }
    
    /// 检查好友申请状态
    private func checkFriendApplicationStatus() {
        print("\n=== 检查好友申请状态 ===")
        
        IMController.shared.getFriendApplicationList { applications in
            print("收到的好友申请数量: \(applications.count)")
            
            if applications.isEmpty {
                print("当前没有待处理的好友申请")
            } else {
                print("找到以下待处理的好友申请:")
                for (index, app) in applications.enumerated() {
                    print("\(index+1). 发送者ID: \(app.fromUserID)")
                    print("   发送者昵称: \(app.fromNickname ?? "未知")")
                    print("   申请消息: \(app.reqMsg ?? "无")")
                    print("   处理状态: \(app.handleResult.rawValue)")
                }
            }
            
            print("===========================\n")
        }
    }

    deinit {
        print("dealloc \(type(of: self))")
        // 移除观察者
        NotificationCenter.default.removeObserver(self)
    }
}

extension FriendListViewController: UITableViewDataSource, UITableViewDelegate {
    public func numberOfSections(in tableView: UITableView) -> Int {
        return isFromTabBar ? _viewModel.lettersRelay.value.count + 1 : _viewModel.lettersRelay.value.count
    }

    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if isFromTabBar && section == 0 {
            return 1
        }
        let actualSection = isFromTabBar ? section - 1 : section
        return _viewModel.contactSections[actualSection].count
    }

    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if isFromTabBar && indexPath.section == 0 {
            let cell = tableView.dequeueReusableCell(withIdentifier: "NewFriendCell", for: indexPath) as! ContactsEntranceTableViewCell
            
            let iconImage = UIImage(nameInBundle: "contact_new_friend_icon")?.resized(to: CGSize(width: 22, height: 22))
            cell.avatarImageView.image = iconImage
            cell.avatarImageView.contentMode = .scaleAspectFit
            
            cell.titleLabel.text = "NEW_FRIEND".innerLocalized()
            cell.titleLabel.font = UIFont(name: "Avenir", size: 15)
            cell.accessoryType = .none
            
            cell.avatarImageView.snp.remakeConstraints { make in
                make.width.height.equalTo(22)
                make.centerY.equalToSuperview()
                make.left.equalToSuperview().offset(16)
            }
            
            return cell
        }
        
        let actualSection = isFromTabBar ? indexPath.section - 1 : indexPath.section
        let cell = tableView.dequeueReusableCell(withIdentifier: FriendListUserTableViewCell.className) as! FriendListUserTableViewCell
        let user: UserInfo = _viewModel.contactSections[actualSection][indexPath.row]
        cell.titleLabel.text = user.nickname
        cell.titleLabel.font = UIFont(name: "Avenir", size: 16)
        cell.avatarImageView.setAvatar(url: user.faceURL, text: user.nickname, onTap: nil)
        return cell
    }

    public func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        if isFromTabBar && indexPath.section == 0 {
            let vc = NewFriendListViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        
        let actualSection = isFromTabBar ? indexPath.section - 1 : indexPath.section
        let user: UserInfo = _viewModel.contactSections[actualSection][indexPath.row]
        if let callBack = selectCallBack {
            callBack(user)
            return
        }
        let vc = UserDetailTableViewController(userId: user.userID, groupId: nil, userDetailFor: .card)
        navigationController?.pushViewController(vc, animated: true)
    }

    public func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        if isFromTabBar && section == 0 {
            return nil
        }
        let actualSection = isFromTabBar ? section - 1 : section
        let name = _viewModel.lettersRelay.value[actualSection]
        let header = ViewUtil.createSectionHeaderWith(text: name)
        return header
    }

    public func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        if isFromTabBar && section == 0 {
            return 0
        }
        return 33
    }

    public func tableView(_: UITableView, heightForFooterInSection _: Int) -> CGFloat {
        return CGFloat.leastNormalMagnitude
    }
}
